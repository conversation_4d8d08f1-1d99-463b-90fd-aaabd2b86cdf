# Production Environment Configuration for CCL Platform
# This is the live production environment configuration

environment: production
region: us-central1

# GCP Project Configuration
gcp:
  project_id: ccl-platform-prod
  region: us-central1
  zone: us-central1-a
  
  # Service Account
  service_account: <EMAIL>
  
  # Artifact Registry
  artifact_registry:
    repository: ccl-prod-services
    location: us-central1
    
  # Cloud Storage
  storage:
    bucket: ccl-prod-storage
    location: us-central1

# Service Configurations
services:
  # Analysis Engine (Rust)
  analysis_engine:
    cloud_run:
      cpu: 4
      memory: 4Gi
      min_instances: 5
      max_instances: 100
      timeout: 300s
      concurrency: 100
    environment_variables:
      RUST_LOG: warn
      MAX_FILE_SIZE: 100MB
      PARSER_TIMEOUT: 120s
    
  # Query Intelligence (Python)
  query_intelligence:
    cloud_run:
      cpu: 8
      memory: 16Gi
      min_instances: 10
      max_instances: 200
      timeout: 600s
      concurrency: 50
    vertex_ai:
      location: us-central1
      model: gemini-2.5-flash-exp
      max_tokens: 8192
    environment_variables:
      PYTHONPATH: /app
      LOG_LEVEL: WARN
      VERTEX_AI_PROJECT: ccl-platform-prod
      VERTEX_AI_LOCATION: us-central1
      
  # Pattern Mining (Python)
  pattern_mining:
    cloud_run:
      cpu: 16
      memory: 32Gi
      min_instances: 3
      max_instances: 50
      timeout: 1800s
      concurrency: 10
    vertex_ai:
      location: us-central1
      training_job_region: us-central1
    environment_variables:
      PYTHONPATH: /app
      LOG_LEVEL: WARN
      ML_MODEL_BUCKET: ccl-prod-ml-models
      TRAINING_DATA_BUCKET: ccl-prod-training-data
      
  # Marketplace (Go)
  marketplace:
    cloud_run:
      cpu: 4
      memory: 8Gi
      min_instances: 10
      max_instances: 200
      timeout: 300s
      concurrency: 1000
    environment_variables:
      GO_ENV: production
      LOG_LEVEL: warn
      STRIPE_WEBHOOK_SECRET: whsec_prod_key
      
  # Web Frontend (TypeScript)
  web:
    cloud_run:
      cpu: 4
      memory: 4Gi
      min_instances: 10
      max_instances: 100
      timeout: 300s
      concurrency: 1000
    environment_variables:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: https://api.ccl-platform.com
      NEXT_PUBLIC_WS_URL: wss://ws.ccl-platform.com
      
  # Collaboration (TypeScript)
  collaboration:
    cloud_run:
      cpu: 8
      memory: 8Gi
      min_instances: 5
      max_instances: 100
      timeout: 300s
      concurrency: 100
    environment_variables:
      NODE_ENV: production
      WS_PORT: 8080
      REDIS_URL: redis://redis-prod.ccl-platform.com:6379

# Database Configuration
databases:
  # Spanner (Primary OLTP) - Multi-regional for high availability
  spanner:
    instance: ccl-prod-instance
    database: ccl-prod-db
    node_count: 10
    processing_units: 5000
    configuration: regional-us-central1
    
  # BigQuery (Analytics)
  bigquery:
    dataset: ccl_prod_analytics
    location: us-central1
    
  # Firestore (Real-time)
  firestore:
    database: ccl-prod-firestore
    location: us-central1
    
  # Redis (Caching) - High availability
  redis:
    instance: ccl-prod-redis
    memory_size_gb: 20
    tier: standard_ha
    location: us-central1
    replica_count: 2

# Networking
networking:
  # VPC
  vpc:
    name: ccl-prod-vpc
    subnet: ccl-prod-subnet
    
  # Load Balancer with CDN
  load_balancer:
    name: ccl-prod-lb
    ssl_certificate: ccl-prod-ssl-cert
    cdn_enabled: true
    
  # Cloud NAT
  nat:
    name: ccl-prod-nat
    router: ccl-prod-router
    
  # Cloud Armor (DDoS protection)
  cloud_armor:
    enabled: true
    policy: ccl-prod-security-policy

# Security Configuration
security:
  # IAM with least privilege
  iam:
    service_accounts:
      - name: ccl-prod-analysis-engine
        roles:
          - roles/spanner.databaseUser
          - roles/storage.objectViewer
      - name: ccl-prod-query-intelligence
        roles:
          - roles/aiplatform.user
          - roles/spanner.databaseUser
      - name: ccl-prod-pattern-mining
        roles:
          - roles/aiplatform.user
          - roles/ml.developer
      - name: ccl-prod-marketplace
        roles:
          - roles/spanner.databaseUser
          - roles/pubsub.publisher
          
  # Secrets with rotation
  secrets:
    - name: database-credentials
      version: latest
      rotation_period: 90d
    - name: api-keys
      version: latest
      rotation_period: 30d
    - name: stripe-keys
      version: latest
      rotation_period: 90d
    - name: jwt-signing-key
      version: latest
      rotation_period: 30d
      
  # VPC Service Controls - Strict
  vpc_service_controls:
    enabled: true
    perimeter: ccl-prod-perimeter
    ingress_policies: restricted
    egress_policies: restricted

# Monitoring and Logging
monitoring:
  # Cloud Monitoring with extended retention
  metrics:
    retention_days: 365
    
  # Cloud Logging with structured logs
  logging:
    retention_days: 365
    log_level: WARN
    structured_logging: true
    
  # Comprehensive Alerting
  alerting:
    notification_channels:
      - email: <EMAIL>
      - slack: "#ccl-prod-alerts"
      - pagerduty: ccl-prod-critical
      - sms: +1-555-0123
    
    alert_policies:
      - name: high_error_rate
        threshold: 5%
        duration: 5m
      - name: high_latency
        threshold: 1000ms
        duration: 5m
      - name: low_availability
        threshold: 99.9%
        duration: 10m
    
  # Uptime Monitoring - Aggressive
  uptime_checks:
    - name: analysis-engine-health
      url: https://analysis-engine.ccl-platform.com/health
      frequency: 10s
      locations: [us-central1, us-east1, europe-west1]
    - name: marketplace-health
      url: https://marketplace.ccl-platform.com/health
      frequency: 10s
      locations: [us-central1, us-east1, europe-west1]
    - name: web-health
      url: https://ccl-platform.com/health
      frequency: 10s
      locations: [us-central1, us-east1, europe-west1]

# CI/CD Configuration
cicd:
  # GitHub Actions with strict controls
  github_actions:
    environment: production
    auto_deploy: false
    require_approval: true
    approvers:
      - platform-team
      - security-team
      - cto
    
  # Deployment Strategy - Blue-Green for critical services
  deployment:
    strategy: blue-green
    health_check_timeout: 1200s
    rollback_on_failure: true
    canary_analysis_duration: 30m
    
  # Testing - Comprehensive
  testing:
    run_integration_tests: true
    run_e2e_tests: true
    performance_testing: true
    load_testing: true
    security_testing: true

# Feature Flags - Production stable
feature_flags:
  debug_mode: false
  verbose_logging: false
  mock_external_apis: false
  
  # Only stable features enabled
  new_pattern_detection: false
  advanced_query_features: true
  real_time_collaboration: true

# Resource Limits - Production scale
resource_limits:
  analysis_engine:
    max_concurrent_analyses: 500
    max_file_size_mb: 500
    
  query_intelligence:
    max_concurrent_queries: 5000
    query_timeout_seconds: 120
    
  pattern_mining:
    max_training_jobs: 20
    max_model_size_gb: 100
    
  marketplace:
    max_concurrent_transactions: 2000
    rate_limit_per_minute: 50000

# Disaster Recovery
disaster_recovery:
  # Multi-region backup
  backup_regions:
    - us-east1
    - europe-west1
    
  # Recovery objectives
  rto_minutes: 60  # Recovery Time Objective
  rpo_minutes: 15  # Recovery Point Objective
  
  # Backup configuration
  backups:
    spanner:
      frequency: hourly
      retention_days: 365
      cross_region: true
    firestore:
      frequency: hourly
      retention_days: 365
      cross_region: true
    storage:
      frequency: daily
      retention_days: 2555  # 7 years
      cross_region: true

# Compliance and Auditing
compliance:
  # SOC2 Type II
  soc2:
    enabled: true
    audit_frequency: annual
    
  # GDPR
  gdpr:
    enabled: true
    data_retention_days: 2555
    right_to_deletion: true
    
  # Audit logging
  audit_logging:
    enabled: true
    retention_days: 2555
    
# Cost Optimization
cost_optimization:
  # Committed use discounts
  committed_use:
    enabled: true
    term: 1_year
    
  # Sustained use discounts
  sustained_use:
    enabled: true
    
  # Budget alerts
  budget_alerts:
    monthly_budget_usd: 50000
    alert_thresholds: [50, 80, 90, 100]
    
  # Resource optimization
  resource_optimization:
    rightsizing_enabled: true
    idle_resource_detection: true
