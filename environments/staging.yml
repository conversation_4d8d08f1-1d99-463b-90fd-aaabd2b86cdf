# Staging Environment Configuration for CCL Platform
# This configuration mirrors production for final testing

environment: staging
region: us-central1

# GCP Project Configuration
gcp:
  project_id: ccl-platform-staging
  region: us-central1
  zone: us-central1-a
  
  # Service Account
  service_account: <EMAIL>
  
  # Artifact Registry
  artifact_registry:
    repository: ccl-staging-services
    location: us-central1
    
  # Cloud Storage
  storage:
    bucket: ccl-staging-storage
    location: us-central1

# Service Configurations
services:
  # Analysis Engine (Rust)
  analysis_engine:
    cloud_run:
      cpu: 2
      memory: 2Gi
      min_instances: 1
      max_instances: 20
      timeout: 300s
      concurrency: 100
    environment_variables:
      RUST_LOG: info
      RUST_BACKTRACE: 1
      MAX_FILE_SIZE: 50MB
      PARSER_TIMEOUT: 60s
    
  # Query Intelligence (Python)
  query_intelligence:
    cloud_run:
      cpu: 4
      memory: 8Gi
      min_instances: 2
      max_instances: 50
      timeout: 600s
      concurrency: 50
    vertex_ai:
      location: us-central1
      model: gemini-2.5-flash-exp
      max_tokens: 8192
    environment_variables:
      PYTHONPATH: /app
      LOG_LEVEL: INFO
      VERTEX_AI_PROJECT: ccl-platform-staging
      VERTEX_AI_LOCATION: us-central1
      
  # Pattern Mining (Python)
  pattern_mining:
    cloud_run:
      cpu: 8
      memory: 16Gi
      min_instances: 1
      max_instances: 10
      timeout: 1800s
      concurrency: 10
    vertex_ai:
      location: us-central1
      training_job_region: us-central1
    environment_variables:
      PYTHONPATH: /app
      LOG_LEVEL: INFO
      ML_MODEL_BUCKET: ccl-staging-ml-models
      TRAINING_DATA_BUCKET: ccl-staging-training-data
      
  # Marketplace (Go)
  marketplace:
    cloud_run:
      cpu: 2
      memory: 4Gi
      min_instances: 2
      max_instances: 30
      timeout: 300s
      concurrency: 1000
    environment_variables:
      GO_ENV: staging
      LOG_LEVEL: info
      STRIPE_WEBHOOK_SECRET: whsec_staging_key
      
  # Web Frontend (TypeScript)
  web:
    cloud_run:
      cpu: 2
      memory: 2Gi
      min_instances: 2
      max_instances: 20
      timeout: 300s
      concurrency: 1000
    environment_variables:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: https://api-staging.ccl-platform.com
      NEXT_PUBLIC_WS_URL: wss://ws-staging.ccl-platform.com
      
  # Collaboration (TypeScript)
  collaboration:
    cloud_run:
      cpu: 4
      memory: 4Gi
      min_instances: 2
      max_instances: 20
      timeout: 300s
      concurrency: 100
    environment_variables:
      NODE_ENV: production
      WS_PORT: 8080
      REDIS_URL: redis://redis-staging.ccl-platform.com:6379

# Database Configuration
databases:
  # Spanner (Primary OLTP)
  spanner:
    instance: ccl-staging-instance
    database: ccl-staging-db
    node_count: 3
    processing_units: 1000
    
  # BigQuery (Analytics)
  bigquery:
    dataset: ccl_staging_analytics
    location: us-central1
    
  # Firestore (Real-time)
  firestore:
    database: ccl-staging-firestore
    location: us-central1
    
  # Redis (Caching)
  redis:
    instance: ccl-staging-redis
    memory_size_gb: 5
    tier: standard_ha
    location: us-central1

# Networking
networking:
  # VPC
  vpc:
    name: ccl-staging-vpc
    subnet: ccl-staging-subnet
    
  # Load Balancer
  load_balancer:
    name: ccl-staging-lb
    ssl_certificate: ccl-staging-ssl-cert
    
  # Cloud NAT
  nat:
    name: ccl-staging-nat
    router: ccl-staging-router

# Security Configuration
security:
  # IAM
  iam:
    service_accounts:
      - name: ccl-staging-analysis-engine
        roles:
          - roles/spanner.databaseUser
          - roles/storage.objectViewer
      - name: ccl-staging-query-intelligence
        roles:
          - roles/aiplatform.user
          - roles/spanner.databaseUser
      - name: ccl-staging-pattern-mining
        roles:
          - roles/aiplatform.user
          - roles/ml.developer
      - name: ccl-staging-marketplace
        roles:
          - roles/spanner.databaseUser
          - roles/pubsub.publisher
          
  # Secrets
  secrets:
    - name: database-credentials
      version: latest
    - name: api-keys
      version: latest
    - name: stripe-keys
      version: latest
    - name: jwt-signing-key
      version: latest
      
  # VPC Service Controls
  vpc_service_controls:
    enabled: true
    perimeter: ccl-staging-perimeter

# Monitoring and Logging
monitoring:
  # Cloud Monitoring
  metrics:
    retention_days: 90
    
  # Cloud Logging
  logging:
    retention_days: 90
    log_level: INFO
    
  # Alerting
  alerting:
    notification_channels:
      - email: <EMAIL>
      - slack: "#ccl-staging-alerts"
      - pagerduty: ccl-staging-service
    
  # Uptime Monitoring
  uptime_checks:
    - name: analysis-engine-health
      url: https://analysis-engine-staging.ccl-platform.com/health
      frequency: 30s
    - name: marketplace-health
      url: https://marketplace-staging.ccl-platform.com/health
      frequency: 30s
    - name: web-health
      url: https://staging.ccl-platform.com/health
      frequency: 30s

# CI/CD Configuration
cicd:
  # GitHub Actions
  github_actions:
    environment: staging
    auto_deploy: true
    require_approval: true
    approvers:
      - qa-team
      - tech-lead
    
  # Deployment Strategy
  deployment:
    strategy: canary
    canary_percentage: 25
    health_check_timeout: 600s
    rollback_on_failure: true
    
  # Testing
  testing:
    run_integration_tests: true
    run_e2e_tests: true
    performance_testing: true
    load_testing: true

# Feature Flags
feature_flags:
  # Production-like features
  debug_mode: false
  verbose_logging: false
  mock_external_apis: false
  
  # Experimental features (mirror production)
  new_pattern_detection: true
  advanced_query_features: true
  real_time_collaboration: true

# Resource Limits
resource_limits:
  # Per-service limits (production-like)
  analysis_engine:
    max_concurrent_analyses: 50
    max_file_size_mb: 100
    
  query_intelligence:
    max_concurrent_queries: 500
    query_timeout_seconds: 60
    
  pattern_mining:
    max_training_jobs: 5
    max_model_size_gb: 20
    
  marketplace:
    max_concurrent_transactions: 200
    rate_limit_per_minute: 5000

# Performance Testing
performance_testing:
  # Load testing configuration
  load_testing:
    enabled: true
    tools:
      - k6
      - artillery
    scenarios:
      - name: normal_load
        duration: 10m
        users: 100
      - name: peak_load
        duration: 5m
        users: 500
      - name: stress_test
        duration: 2m
        users: 1000
        
  # Performance benchmarks
  benchmarks:
    analysis_engine:
      target_p95_latency_ms: 2000
      target_throughput_rps: 100
    query_intelligence:
      target_p95_latency_ms: 500
      target_throughput_rps: 200
    marketplace:
      target_p95_latency_ms: 100
      target_throughput_rps: 1000

# Data Management
data_management:
  # Backup configuration
  backups:
    spanner:
      frequency: daily
      retention_days: 30
    firestore:
      frequency: daily
      retention_days: 30
      
  # Data refresh from production
  data_refresh:
    enabled: true
    frequency: weekly
    anonymization: true
    
# Cost Optimization
cost_optimization:
  # Auto-scaling
  auto_scaling:
    scale_to_zero: false  # Keep minimum instances
    scale_down_delay: 600s
    
  # Resource scheduling
  resource_scheduling:
    shutdown_unused_resources: false
    
  # Budget alerts
  budget_alerts:
    monthly_budget_usd: 2000
    alert_thresholds: [50, 80, 100]
