I am working on updating the dependencies of a Rust service called `analysis-engine`. I have updated the `Cargo.toml` file with the latest versions of the dependencies, but I am now facing several build errors. The errors are primarily in the `services/analysis-engine/src/storage/spanner.rs` file, which is responsible for interacting with Google Cloud Spanner.

The main issue seems to be with the `gcloud-spanner` and `gcloud-sdk` crates. I have tried to fix the imports, but I am still getting errors related to unresolved imports and trait implementation issues.

Here is the relevant information:

*   **`Cargo.toml`:**
    ```toml
    [package]
    name = "analysis-engine"
    version = "0.1.0"
    edition = "2021"

    [dependencies]
    # Web framework
    axum = { version = "0.8.4", features = ["ws", "macros"] }
    tower = "0.5"
    tower-http = { version = "0.6.6", features = ["cors", "trace", "compression-full"] }
    hyper = { version = "1.0", features = ["full"] }

    # Async runtime
    tokio = { version = "1.46.1", features = ["full"] }
    futures = "0.3"

    # Code parsing
    tree-sitter = "0.25.6"
    tree-sitter-rust = "0.24"
    tree-sitter-javascript = "0.23"
    tree-sitter-typescript = "0.23"
    tree-sitter-python = "0.23"
    tree-sitter-go = "0.23"
    tree-sitter-java = "0.23"
    tree-sitter-c = "0.24"
    tree-sitter-cpp = "0.23"

    # Serialization
    serde = { version = "1.0.219", features = ["derive"] }
    serde_json = "1.0"

    # Error handling
    anyhow = "1.0.98"
    thiserror = "1.0"

    # Logging
    tracing = "0.1.41"
    tracing-subscriber = { version = "0.3", features = ["env-filter"] }

    # GCP clients
    gcloud-spanner = "1.3.0"
    gcloud-storage = "1.1.0"
    google-cloud-pubsub = "0.30.0"
    google-cloud-gax = "0.19.1"

    # Hashing
    sha2 = "0.10"

    # WebSocket
    tokio-tungstenite = "0.27.0"

    # Parallel processing
    rayon = "1.10.0"

    # Utilities
    uuid = { version = "1.17.0", features = ["serde", "v4"] }
    chrono = { version = "0.4.41", features = ["serde"] }
    dashmap = "6.1.0"
    once_cell = "1.19"
    rand = "0.8"
    google-cloud-auth = "0.16"

    # Git operations
    git2 = "0.19"

    # Authentication
    jsonwebtoken = "9.2"

    # Metrics
    prometheus = "0.13"

    # HTTP client
    reqwest = { version = "0.11", features = ["json"] }

    # Rate limiting
    governor = "0.6"
    nonzero_ext = "0.3"

    # Path operations
    walkdir = "2.4"

    # Language detection
    tokei = "12.1"

    # Pattern matching
    glob-match = "0.2"

    # Configuration
    config = "0.13"

    # Google Cloud APIs
    google-cloud-googleapis = { version = "0.16.1", features = ["pubsub"] }
    gcloud-sdk = "0.27.3"

    [dev-dependencies]
    criterion = { version = "0.5", features = ["html_reports"] }
    mockall = "0.12"
    tempfile = "3.8"
    wiremock = "0.5"

    [[bench]]
    name = "analysis_bench"
    harness = false

    [profile.release]
    opt-level = 3
    lto = true
    codegen-units = 1
    ```
*   **`services/analysis-engine/src/storage/spanner.rs`:**
    ```rust
    use gcloud_spanner::client::{Client, Error as SpannerError};
    use gcloud_spanner::statement::Statement;
    use gcloud_spanner::session::SessionError;
    use gcloud_spanner::transaction_rw::ReadWriteTransaction;
    use gcloud_spanner::apiv1::conn_pool::TryAs;
    use gcloud_spanner::apiv1::spanner_client::SpannerClient;
    use gcloud_spanner::apiv1::tonic::Status;
    use crate::models::{AnalysisResult, AnalysisStatus, ListAnalysesParams, RepositoryMetrics, DetectedPattern};
    use anyhow::Result;

    // Custom error type for Spanner transactions
    #[derive(thiserror::Error, Debug)]
    pub enum TransactionError {
        #[error("JSON serialization error: {0}")]
        Json(#[from] serde_json::Error),
        #[error(transparent)]
        Spanner(#[from] SpannerError),
    }

    impl TryAs<Status> for TransactionError {
        fn try_as(&self) -> Option<&Status> {
            match self {
                TransactionError::Spanner(SpannerError::GRPC(status)) => Some(status),
                _ => None,
            }
        }
    }

    impl From<Status> for TransactionError {
        fn from(status: Status) -> Self {
            Self::Spanner(SpannerError::GRPC(status))
        }
    }

    impl From<tonic::Status> for TransactionError {
        fn from(status: tonic::Status) -> Self {
            Self::Spanner(SpannerError::GRPC(status.into()))
        }
    }

    impl From<SessionError> for TransactionError {
        fn from(se: SessionError) -> Self {
            Self::Spanner(SpannerError::InvalidSession(se))
        }
    }

    pub struct SpannerOperations {
        client: Client,
        database: String,
        project_id: String,
        instance_id: String,
        database_id: String,
    }

    impl SpannerOperations {
        pub async fn new(client: Client) -> Result<Self> {
            let project_id = std::env::var("GCP_PROJECT_ID").expect("GCP_PROJECT_ID not set");
            let instance_id = std::env::var("SPANNER_INSTANCE_ID").unwrap_or_else(|_| "ccl-production".to_string());
            let database_id = std::env::var("SPANNER_DATABASE_ID").unwrap_or_else(|_| "analysis-engine".to_string());
            let database = format!("projects/{}/instances/{}/databases/{}", project_id, instance_id, database_id);

            Ok(Self {
                client,
                database,
                project_id,
                instance_id,
                database_id,
            })
        }

        pub async fn store_analysis(&self, analysis: &AnalysisResult) -> Result<()> {
            use std::sync::Arc;

            // Clone the necessary data and wrap in Arc for sharing
            let analysis_id = Arc::new(analysis.id.clone());
            let repository_url = Arc::new(analysis.repository_url.clone());
            let branch = Arc::new(analysis.branch.clone());
            let status = Arc::new(analysis.status.to_string());
            let started_at = Arc::new(analysis.started_at.to_rfc3339());
            let completed_at = Arc::new(analysis.completed_at.map(|t| t.to_rfc3339()));
            let duration_seconds = Arc::new(analysis.duration_seconds);
            let metrics = Arc::new(serde_json::to_string(&analysis.metrics)?);
            let patterns = Arc::new(serde_json::to_string(&analysis.patterns)?);
            let languages = Arc::new(serde_json::to_string(&analysis.languages)?);
            let embeddings = Arc::new(serde_json::to_string(&analysis.embeddings)?);
            let error_message = Arc::new(analysis.error_message.clone());
            let user_id = Arc::new(analysis.user_id.clone());
            let file_count = Arc::new(analysis.file_count as i64);
            let success_rate = Arc::new(analysis.success_rate);

            self.client
                .read_write_transaction(|tx| {
                    let analysis_id = analysis_id.clone();
                    let repository_url = repository_url.clone();
                    let branch = branch.clone();
                    let status = status.clone();
                    let started_at = started_at.clone();
                    let completed_at = completed_at.clone();
                    let duration_seconds = duration_seconds.clone();
                    let metrics = metrics.clone();
                    let patterns = patterns.clone();
                    let languages = languages.clone();
                    let embeddings = embeddings.clone();
                    let error_message = error_message.clone();
                    let user_id = user_id.clone();
                    let file_count = file_count.clone();
                    let success_rate = success_rate.clone();

                    Box::pin(async move {
                        let mut statement = Statement::new(
                            "INSERT INTO analyses (analysis_id, repository_url, branch, status, started_at, completed_at, duration_seconds, metrics, patterns, languages, embeddings, error_message, user_id, file_count, success_rate)
                             VALUES (@analysis_id, @repository_url, @branch, @status, @started_at, @completed_at, @duration_seconds, @metrics, @patterns, @languages, @embeddings, @error_message, @user_id, @file_count, @success_rate)
                             ON DUPLICATE KEY UPDATE
                             status = @status, completed_at = @completed_at, duration_seconds = @duration_seconds, metrics = @metrics, patterns = @patterns, languages = @languages, embeddings = @embeddings, error_message = @error_message, success_rate = @success_rate"
                        );
                        statement.add_param("analysis_id", &*analysis_id);
                        statement.add_param("repository_url", &*repository_url);
                        statement.add_param("branch", &*branch);
                        statement.add_param("status", &*status);
                        statement.add_param("started_at", &*started_at);
                        if let Some(completed_at) = &*completed_at {
                            statement.add_param("completed_at", completed_at);
                        } else {
                            statement.add_param("completed_at", &Option::<String>::None);
                        }
                        if let Some(duration) = *duration_seconds {
                            statement.add_param("duration_seconds", &(duration as f64));
                        } else {
                            statement.add_param("duration_seconds", &Option::<f64>::None);
                        }
                        statement.add_param("metrics", &*metrics);
                        statement.add_param("patterns", &*patterns);
                        statement.add_param("languages", &*languages);
                        statement.add_param("embeddings", &*embeddings);
                        statement.add_param("error_message", &*error_message);
                        statement.add_param("user_id", &*user_id);
                        statement.add_param("file_count", &*file_count);
                        statement.add_param("success_rate", &*success_rate);

                        tx.update(statement).await?;
                        Ok::<(), TransactionError>(())
                    })
                })
                .await?;
            Ok(())
        }

        pub async fn get_analysis(&self, analysis_id: &str) -> Result<Option<AnalysisResult>> {
            let mut tx = self.client.read_only_transaction().await?;
            let mut statement = Statement::new("SELECT * FROM analyses WHERE analysis_id = @analysis_id");
            statement.add_param("analysis_id", &analysis_id.to_string());

            let mut reader = tx.query(statement).await?;
            if let Some(row) = reader.next().await? {
                let analysis: AnalysisResult = row.try_into()?;
                return Ok(Some(analysis));
            }
            Ok(None)
        }

        pub async fn list_analyses(&self, params: &ListAnalysesParams) -> Result<Vec<AnalysisResult>> {
            let mut tx = self.client.read_only_transaction().await?;
            let mut sql = "SELECT * FROM analyses WHERE 1=1".to_string();
            let mut statement = Statement::new("");

            // Use parameterized queries to prevent SQL injection
            if let Some(status) = &params.status {
                sql.push_str(" AND status = @status");
                statement.add_param("status", &status.to_string());
            }
            if let Some(repo_url) = &params.repository_url {
                sql.push_str(" AND repository_url = @repository_url");
                statement.add_param("repository_url", repo_url);
            }
            if let Some(created_after) = &params.created_after {
                sql.push_str(" AND started_at >= @created_after");
                statement.add_param("created_after", &created_after.to_rfc3339());
            }
            if let Some(created_before) = &params.created_before {
                sql.push_str(" AND started_at <= @created_before");
                statement.add_param("created_before", &created_before.to_rfc3339());
            }

            sql.push_str(" ORDER BY started_at DESC");

            let page = params.page.unwrap_or(1);
            let per_page = params.per_page.unwrap_or(20).min(100); // Cap at 100 for safety
            let offset = (page - 1) * per_page;

            sql.push_str(" LIMIT @limit OFFSET @offset");

            // Create a new statement with the complete SQL
            let mut statement = Statement::new(&sql);

            // Re-add all the parameters
            if let Some(status) = &params.status {
                statement.add_param("status", &status.to_string());
            }
            if let Some(repo_url) = &params.repository_url {
                statement.add_param("repository_url", repo_url);
            }
            if let Some(created_after) = &params.created_after {
                statement.add_param("created_after", &created_after.to_rfc3339());
            }
            if let Some(created_before) = &params.created_before {
                statement.add_param("created_before", &created_before.to_rfc3339());
            }
            statement.add_param("limit", &(per_page as i64));
            statement.add_param("offset", &(offset as i64));

            let mut reader = tx.query(statement).await?;
            let mut results = Vec::new();
            while let Some(row) = reader.next().await? {
                let analysis: AnalysisResult = row.try_into()?;
                results.push(analysis);
            }
            Ok(results)
        }

        pub async fn get_analysis_metrics(&self, analysis_id: &str) -> Result<Option<RepositoryMetrics>> {
            let mut tx = self.client.read_only_transaction().await?;
            let mut statement = Statement::new("SELECT metrics FROM analyses WHERE analysis_id = @analysis_id");
            statement.add_param("analysis_id", &analysis_id.to_string());

            let mut reader = tx.query(statement).await?;
            if let Some(row) = reader.next().await? {
                let metrics_json: String = row.column_by_name("metrics")?;
                let metrics: RepositoryMetrics = serde_json::from_str(&metrics_json)?;
                return Ok(Some(metrics));
            }
            Ok(None)
        }

        pub async fn get_analysis_patterns(&self, analysis_id: &str) -> Result<Option<Vec<DetectedPattern>>> {
            let mut tx = self.client.read_only_transaction().await?;
            let mut statement = Statement::new("SELECT patterns FROM analyses WHERE analysis_id = @analysis_id");
            statement.add_param("analysis_id", &analysis_id.to_string());

            let mut reader = tx.query(statement).await?;
            if let Some(row) = reader.next().await? {
                let patterns_json: String = row.column_by_name("patterns")?;
                let patterns: Vec<DetectedPattern> = serde_json::from_str(&patterns_json)?;
                return Ok(Some(patterns));
            }
            Ok(None)
        }

        pub async fn health_check(&self) -> Result<()> {
            // Simple health check - execute a basic query
            let mut tx = self.client.read_only_transaction().await?;
            let statement = Statement::new("SELECT 1");
            let mut reader = tx.query(statement).await?;

            // Just check if we can execute the query without errors
            if reader.next().await?.is_some() {
                Ok(())
            } else {
                Err(anyhow::anyhow!("Spanner health check failed: no response"))
            }
        }

        pub async fn read_only_transaction(&self) -> Result<ReadWriteTransaction> {
            // For now we return a ReadWriteTransaction since the API doesn't expose a generic Transaction type
            // In practice, we should use read-only transactions for read operations
            self.client.begin_read_write_transaction().await
                .map_err(|e| anyhow::anyhow!("Failed to create transaction: {}", e))
        }
    }

    // Implement TryFrom<Row> for AnalysisResult
    impl TryFrom<gcloud_spanner::row::Row> for AnalysisResult {
        type Error = SpannerError;

        fn try_from(row: gcloud_spanner::row::Row) -> Result<Self, Self::Error> {
            use chrono::{DateTime, Utc};

            let id: String = row.column_by_name("analysis_id")?;
            let repository_url: String = row.column_by_name("repository_url")?;
            let branch: String = row.column_by_name("branch")?;
            let status_str: String = row.column_by_name("status")?;
            let status = match status_str.as_str() {
                "pending" => AnalysisStatus::Pending,
                "inprogress" => AnalysisStatus::InProgress,
                "completed" => AnalysisStatus::Completed,
                "failed" => AnalysisStatus::Failed,
                _ => AnalysisStatus::Failed,
            };

            let started_at_str: String = row.column_by_name("started_at")?;
            let started_at = DateTime::parse_from_rfc3339(&started_at_str)?.with_timezone(&Utc);

            let completed_at: Option<DateTime<Utc>> = row.column_by_name::<Option<String>>("completed_at")?
                .map(|s| DateTime::parse_from_rfc3339(&s).map(|dt| dt.with_timezone(&Utc)))
                .transpose()
                .map_err(|e: chrono::ParseError| SpannerError::Client(e.to_string()))?
                .flatten();

            let duration_seconds: Option<f64> = row.column_by_name("duration_seconds")?;
            let duration_seconds = duration_seconds.map(|d| d as u64);

            let metrics_json: String = row.column_by_name("metrics")?;
            let metrics: Option<RepositoryMetrics> = Some(serde_json::from_str(&metrics_json).map_err(|e| SpannerError::Decode(e.to_string()))?);

            let patterns_json: String = row.column_by_name("patterns")?;
            let patterns: Vec<DetectedPattern> = serde_json::from_str(&patterns_json).map_err(|e| SpannerError::Decode(e.to_string()))?;

            let languages_json: String = row.column_by_name("languages")?;
            let languages: std::collections::HashMap<String, crate::models::LanguageStats> = serde_json::from_str(&languages_json).map_err(|e| SpannerError::Decode(e.to_string()))?;

            let embeddings_json: String = row.column_by_name("embeddings")?;
            let embeddings: Option<Vec<crate::models::CodeEmbedding>> = Some(serde_json::from_str(&embeddings_json).map_err(|e| SpannerError::Decode(e.to_string()))?);

            let error_message: Option<String> = row.column_by_name("error_message")?;
            let user_id: String = row.column_by_name("user_id")?;
            let file_count: i64 = row.column_by_name("file_count")?;
            let success_rate: f64 = row.column_by_name("success_rate")?;

            Ok(AnalysisResult {
                id,
                repository_url,
                branch,
                status,
                started_at,
                completed_at,
                duration_seconds,
                progress: None,
                current_stage: None,
                estimated_completion: None,
                metrics,
                patterns,
                languages,
                embeddings,
                error_message,
                failed_files: Vec::new(),
                user_id,
                webhook_url: None,
                file_count: file_count as usize,
                success_rate,
                performance_metrics: None,
            })
        }
    }
    ```
*   **Build errors:**
    ```
    error[E0432]: unresolved import `gcloud_sdk::retry`
     --> src/storage/spanner.rs:7:17
      |
    7 | use gcloud_sdk::retry::TryAs;
      |                 ^^^^^ could not find `retry` in `gcloud_sdk`

    error[E0433]: failed to resolve: use of unresolved module or unlinked crate `tonic`
      --> src/storage/spanner.rs:35:11
       |
    35 | impl From<tonic::Status> for TransactionError {
       |           ^^^^^ use of unresolved module or unlinked crate `tonic`
       |
       = help: if you wanted to use a crate named `tonic`, use `cargo add tonic` to add it to your `Cargo.toml`
    help: consider importing this crate
       |
    1  + use gcloud_sdk::tonic;
       |

    error[E0433]: failed to resolve: use of unresolved module or unlinked crate `tonic`
      --> src/storage/spanner.rs:36:21
       |
    36 |     fn from(status: tonic::Status) -> Self {
       |                     ^^^^^ use of unresolved module or unlinked crate `tonic`
       |
       = help: if you wanted to use a crate named `tonic`, use `cargo add tonic` to add it to your `Cargo.toml`
    help: consider importing this crate
       |
    1  + use gcloud_sdk::tonic;
       |

    error[E0603]: struct `SpannerClient` is private
     --> src/storage/spanner.rs:6:44
      |
    6 | use gcloud_spanner::apiv1::spanner_client::SpannerClient;
      |                                            ^^^^^^^^^^^^^ private struct
      |
    note: the struct `SpannerClient` is defined here
     --> /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/gcloud-spanner-1.3.0/src/apiv1/spanner_client.rs:8:5
      |
    8 | use google_cloud_googleapis::spanner::v1::spanner_client::SpannerClient;
      |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    help: import `SpannerClient` directly
      |
    6 - use gcloud_spanner::apiv1::spanner_client::SpannerClient;
    6 + use gcloud_googleapis::spanner::v1::spanner_client::SpannerClient;
      |

    error[E0277]: the trait bound `TransactionError: gcloud_gax::retry::TryAs<gcloud_sdk::tonic::Status>` is not satisfied
       --> src/storage/spanner.rs:92:14
        |
    92  |             .read_write_transaction(|tx| {
        |              ^^^^^^^^^^^^^^^^^^^^^^ the trait `gcloud_gax::retry::TryAs<gcloud_sdk::tonic::Status>` is not implemented for `TransactionError`
        |
        = help: the following other types implement trait `gcloud_gax::retry::TryAs<T>`:
                  SessionError
                  gcloud_sdk::tonic::Status
                  gcloud_spanner::client::Error
    note: required by a bound in `gcloud_spanner::client::Client::read_write_transaction`
       --> /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/gcloud-spanner-1.3.0/src/client.rs:488:12
        |
    486 |     pub async fn read_write_transaction<'a, T, E, F>(&self, f: F) -> Result<(CommitResult, T), E>
        |                  ---------------------- required by a bound in this associated function
    487 |     where
    488 |         E: TryAs<Status> + From<SessionError> + From<Status>,
        |            ^^^^^^^^^^^^^ required by this bound in `Client::read_write_transaction`

    error[E0277]: the trait bound `TransactionError: gcloud_gax::retry::TryAs<gcloud_sdk::tonic::Status>` is not satisfied
       --> src/storage/spanner.rs:91:9
        |
    91  | /         self.client
    92  | |             .read_write_transaction(|tx| {
    93  | |                 let analysis_id = analysis_id.clone();
    94  | |                 let repository_url = repository_url.clone();
    ...   |
    142 | |                 })
    143 | |             })
        | |______________^ the trait `gcloud_gax::retry::TryAs<gcloud_sdk::tonic::Status>` is not implemented for `TransactionError`
        |
        = help: the following other types implement trait `gcloud_gax::retry::TryAs<T>`:
                  SessionError
                  gcloud_sdk::tonic::Status
                  gcloud_spanner::client::Error
    note: required by a bound in `gcloud_spanner::client::Client::read_write_transaction`
       --> /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/gcloud-spanner-1.3.0/src/client.rs:488:12
        |
    486 |     pub async fn read_write_transaction<'a, T, E, F>(&self, f: F) -> Result<(CommitResult, T), E>
        |                  ---------------------- required by a bound in this associated function
    487 |     where
    488 |         E: TryAs<Status> + From<SessionError> + From<Status>,
        |            ^^^^^^^^^^^^^ required by this bound in `Client::read_write_transaction`

    error[E0277]: the trait bound `TransactionError: gcloud_gax::retry::TryAs<gcloud_sdk::tonic::Status>` is not satisfied
       --> src/storage/spanner.rs:144:14
        |
    144 |             .await?;
        |              ^^^^^ the trait `gcloud_gax::retry::TryAs<gcloud_sdk::tonic::Status>` is not implemented for `TransactionError`
        |
        = help: the following other types implement trait `gcloud_gax::retry::TryAs<T>`:
                  SessionError
                  gcloud_sdk::tonic::Status
                  gcloud_spanner::client::Error
    note: required by a bound in `gcloud_spanner::client::Client::read_write_transaction`
       --> /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/gcloud-spanner-1.3.0/src/client.rs:488:12
        |
    486 |     pub async fn read_write_transaction<'a, T, E, F>(&self, f: F) -> Result<(CommitResult, T), E>
        |                  ---------------------- required by a bound in this associated function
    487 |     where
    488 |         E: TryAs<Status> + From<SessionError> + From<Status>,
        |            ^^^^^^^^^^^^^ required by this bound in `Client::read_write_transaction`
    ```

Please analyze the build errors and the provided code and suggest a solution. I need to fix the unresolved imports and the trait implementation issues. I believe the main issue is with the `gcloud-sdk` and `gcloud-spanner` crates, which seem to have a different structure than I expected.
