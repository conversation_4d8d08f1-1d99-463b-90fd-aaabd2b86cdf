name: "Query Intelligence Service Implementation"
description: |
  Implementation of the Python-based query intelligence service that processes natural language queries,
  performs semantic search, and generates intelligent responses using Vertex AI and Gemini 2.5
---

## Goal
Implement the Query Intelligence service as the natural language processing core of the CCL platform, capable of understanding developer queries, performing semantic code search, and generating contextual responses with high accuracy and sub-100ms response times.

## Why
- **Core AI Capability**: Foundation for natural language interaction with codebases
- **User Experience**: Primary interface for developers to understand code
- **Business Critical**: Key differentiator for CCL platform
- **Performance Critical**: Must respond <100ms for real-time interaction

## What
A high-performance Python service that:
- Processes natural language queries about code
- Performs semantic search across code embeddings
- Integrates with Vertex AI and Gemini 2.5 for understanding
- Generates contextual responses with code references
- Provides confidence scoring and reasoning
- Supports real-time streaming responses

### Success Criteria
- [ ] Service responds to queries <100ms (p95)
- [ ] Query understanding accuracy >95%
- [ ] Semantic search relevance >90%
- [ ] Integration with Vertex AI working
- [ ] Gemini 2.5 integration functional
- [ ] Streaming responses implemented
- [ ] Confidence scoring accurate (>85% correlation)
- [ ] Concurrent queries: 1000+ per second
- [ ] Memory usage <2GB per instance
- [ ] 99.95% uptime requirement met

## All Needed Context

### Service Specifications
```yaml
Service Details:
  name: query-intelligence
  language: Python 3.11+
  runtime: Cloud Run
  port: 8002
  
Architecture:
  pattern: microservice
  communication: REST + WebSocket + Events
  data_store: Redis (cache only)
  service_boundaries: strict
  dependencies:
    - Vertex AI (Gemini 2.5)
    - Spanner (context storage)
    - Memorystore (cache)
    - BigQuery (analytics)
  
Performance:
  slo_response_time: <100ms (p95)
  slo_availability: 99.95%
  scaling: 0-5000 instances
  memory: 8GB per instance
  cpu: 4 vCPU per instance
  concurrent_queries: 1000+ per second
  max_query_length: 10KB
  streaming_latency: <10ms
```

### Technology Stack
```yaml
Primary Language: Python 3.11+
Framework: FastAPI (async web framework)
AI/ML: Vertex AI, Gemini 2.5, LangChain
Database: None (accesses data via service APIs)
Cache: Redis (Memorystore)
Dependencies:
  - fastapi: 0.104+ # Web framework
  - uvicorn: 0.24+ # ASGI server
  - google-cloud-aiplatform: 1.38+ # Vertex AI client
  - redis: 5.0+ # Cache client
  - langchain: 0.1+ # LLM framework
  - pydantic: 2.5+ # Data validation
  - numpy: 1.24+ # Numerical computing
  - sentence-transformers: 2.2+ # Embeddings
  - websockets: 12.0+ # WebSocket support
  - httpx: 0.25+ # HTTP client for service communication
  - aiofiles: 23.2+ # Async file operations
  - prometheus-client: 0.19+ # Metrics collection
  - google-cloud-pubsub: 2.18+ # Event messaging
  
Development Tools:
  - pytest: Testing framework
  - black: Code formatting
  - ruff: Linting
  - mypy: Type checking
```

### Current Codebase Context
```bash
# Reference implementation patterns from:
examples/query-intelligence/
├── query_processor.py      # Query processing patterns
├── vector_search.py        # Semantic search implementation
├── llm_integration.py      # Vertex AI integration patterns
└── response_generator.py   # Response generation patterns
```

### Desired Service Structure
```bash
services/query-intelligence/
├── pyproject.toml          # Python dependencies
├── Dockerfile              # Container definition
├── cloudbuild.yaml         # Build configuration
├── src/
│   ├── query_intelligence/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI application
│   │   ├── config/         # Configuration management
│   │   │   ├── __init__.py
│   │   │   └── settings.py
│   │   ├── api/            # API endpoints
│   │   │   ├── __init__.py
│   │   │   ├── query.py    # Query endpoints
│   │   │   ├── health.py   # Health check
│   │   │   └── websocket.py # WebSocket endpoints
│   │   ├── services/       # Business logic
│   │   │   ├── __init__.py
│   │   │   ├── query_processor.py # Core query processing
│   │   │   ├── semantic_search.py # Vector search
│   │   │   ├── llm_service.py     # LLM integration
│   │   │   └── response_generator.py # Response generation
│   │   ├── models/         # Data models
│   │   │   ├── __init__.py
│   │   │   ├── query.py    # Query models
│   │   │   ├── response.py # Response models
│   │   │   └── embeddings.py # Embedding models
│   │   ├── clients/        # External service clients
│   │   │   ├── __init__.py
│   │   │   ├── vertex_ai.py # Vertex AI client
│   │   │   ├── spanner.py  # Database client
│   │   │   ├── firestore.py # Real-time database
│   │   │   └── redis.py    # Cache client
│   │   └── utils/          # Utility functions
│   │       ├── __init__.py
│   │       ├── embeddings.py # Embedding utilities
│   │       └── metrics.py  # Performance metrics
├── tests/                  # Test files
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── fixtures/          # Test data
└── docs/
    ├── README.md          # Service documentation
    └── api.md             # API documentation
```

### Integration Requirements
```yaml
Service Integrations:
  analysis-engine:
    access_method: REST API calls
    endpoints: GET /analysis/{id}, GET /embeddings/{id}
    auth: service-to-service JWT
    
  pattern-mining:
    access_method: REST API calls  
    endpoints: GET /patterns/search, GET /patterns/recommendations
    auth: service-to-service JWT
    
  collaboration:
    access_method: REST API calls
    endpoints: POST /conversations, GET /conversations/{id}
    auth: service-to-service JWT
  
Event Subscriptions:
  - analysis.completed: New code analysis available
  - pattern.detected: New patterns for query enhancement
  
Event Publications:
  - query.processed: Query processing completed
  - query.failed: Query processing failed
  
External APIs:
  - Vertex AI: LLM inference and embeddings
  - Gemini 2.5: Advanced language understanding
  - Redis: Query result caching
  
Data Access Rules:
  - NO direct database access to Spanner/Firestore
  - Data ONLY through service APIs with proper authentication
  - Use Redis ONLY for caching, not persistent storage
  - All cross-service calls must use circuit breakers
```

### Known Gotchas & Library Quirks
```yaml
Python-Specific:
  - Async/await: All I/O operations must be async for performance
  - Memory management: Use connection pooling for database clients
  - GIL limitations: Use asyncio for I/O bound operations
  
Vertex AI:
  - Rate limits: Implement exponential backoff and request batching
  - Model loading: Cache model instances to avoid cold starts
  - Token limits: Implement intelligent text chunking for large queries
  
Vector Search:
  - Embedding dimensions: Ensure consistency across all embeddings
  - Similarity thresholds: Tune based on query type and context
  - Index updates: Handle real-time embedding updates efficiently
  
Google Cloud:
  - Spanner: Use read-only transactions for better performance
  - Firestore: Implement proper indexing for real-time queries
  - Redis: Use connection pooling and handle failover gracefully
  
Performance:
  - Query parsing: Cache parsed queries to avoid reprocessing
  - Embedding generation: Batch embeddings for efficiency
  - Response streaming: Use Server-Sent Events for real-time updates
```

## Implementation Blueprint

### Phase 1: Service Foundation
1. **Project Setup**
   ```bash
   mkdir -p services/query-intelligence/src/query_intelligence
   cd services/query-intelligence
   ```

2. **Core Dependencies**
   - FastAPI for async web framework
   - Vertex AI client for LLM integration
   - Spanner and Firestore clients
   - Redis for caching

3. **Basic Service Structure**
   - FastAPI application setup
   - Health check endpoints
   - Configuration management
   - Logging and metrics

### Phase 2: Query Processing Engine
1. **Query Understanding**
   - Natural language query parsing
   - Intent classification
   - Entity extraction
   - Query normalization

2. **Semantic Search**
   - Vector similarity search
   - Embedding generation
   - Result ranking
   - Context filtering

3. **LLM Integration**
   - Vertex AI client setup
   - Gemini 2.5 integration
   - Prompt engineering
   - Response generation

### Phase 3: API Implementation
1. **REST API Endpoints**
   ```python
   # POST /query - Process natural language query
   # GET /query/{id} - Get query results
   # POST /query/stream - Streaming query processing
   # GET /health - Health check
   ```

2. **WebSocket Support**
   - Real-time query processing
   - Streaming responses
   - Session management

3. **Authentication & Authorization**
   - API key validation
   - Rate limiting
   - Request logging

### Phase 4: Performance Optimization
1. **Caching Strategy**
   - Query result caching
   - Embedding caching
   - Model response caching

2. **Async Processing**
   - Connection pooling
   - Batch processing
   - Background tasks

3. **Monitoring Integration**
   - Performance metrics
   - Error tracking
   - Query analytics

## Validation Gates

### Development Validation
```bash
# Code Quality
ruff check src/
black --check src/
mypy src/

# Unit Tests
pytest tests/unit/ -v --cov=src

# Integration Tests
pytest tests/integration/ -v

# Security Scan
bandit -r src/

# Performance Tests
pytest tests/performance/ -v
```

### API Validation
```bash
# Health Check
curl -f http://localhost:8002/health

# Query Endpoint
curl -X POST http://localhost:8002/query \
  -H "Content-Type: application/json" \
  -d '{"query": "How does authentication work in this codebase?"}'

# WebSocket Test
wscat -c ws://localhost:8002/ws/query

# Load Testing
locust -f tests/load/locustfile.py --host=http://localhost:8002
```

### AI Integration Validation
```bash
# Vertex AI Connection
python -c "from src.query_intelligence.clients.vertex_ai import VertexAIClient; client = VertexAIClient(); print('Connected')"

# Embedding Generation
python tests/integration/test_embeddings.py

# Query Processing
python tests/integration/test_query_processing.py

# Response Quality
python tests/integration/test_response_quality.py
```

## Success Metrics

### Performance Metrics
- **Query Response Time**: <100ms (p95)
- **Semantic Search Accuracy**: >90%
- **Query Understanding Accuracy**: >95%
- **Memory Usage**: <2GB per instance

### Quality Metrics
- **Response Relevance**: >90%
- **Confidence Score Accuracy**: >85%
- **Error Rate**: <1%
- **Test Coverage**: >90%

### Business Metrics
- **Query Success Rate**: >98%
- **User Satisfaction**: >4.5/5
- **Response Completeness**: >95%
- **Context Accuracy**: >90%

## Final Validation Checklist
- [ ] All unit tests pass (>90% coverage)
- [ ] Integration tests pass
- [ ] Performance benchmarks meet SLO
- [ ] AI integration working correctly
- [ ] API documentation complete
- [ ] Service documentation updated
- [ ] Monitoring configured
- [ ] Deployment successful
- [ ] Health checks passing
- [ ] Load testing completed
- [ ] Security scan passes
- [ ] Error handling validated

---

## Implementation Notes

### Pattern References
- Follow async patterns from `examples/query-intelligence/`
- Use FastAPI best practices for API design
- Implement proper error handling with custom exceptions
- Use structured logging with correlation IDs

### AI/ML Requirements
- Implement proper prompt engineering for Gemini 2.5
- Use efficient embedding generation and caching
- Handle model rate limits and failures gracefully
- Implement confidence scoring for all responses

### Performance Requirements
- Use async/await for all I/O operations
- Implement connection pooling for all clients
- Cache frequently accessed data in Redis
- Monitor and optimize query processing times
- Use streaming for large responses
