# CCL Platform Environment Configuration
# Copy this file to .env.development for local development
# Copy this file to .env.production and update values for production

# Environment
ENV=development
PROJECT_ID=ccl-local
REGION=us-central1

# Database Configuration
DATABASE_URL=postgresql://ccl_dev:dev_password@localhost:5432/ccl_local
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=ccl_local
DATABASE_USER=ccl_dev
DATABASE_PASSWORD=dev_password
DATABASE_SSL_MODE=disable
DATABASE_MAX_CONNECTIONS=100
DATABASE_POOL_SIZE=20

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=50

# Google Cloud Platform Emulators (Development)
SPANNER_EMULATOR_HOST=localhost:9010
FIRESTORE_EMULATOR_HOST=localhost:8080
PUBSUB_EMULATOR_HOST=localhost:8085
STORAGE_EMULATOR_HOST=http://localhost:4443

# Google Cloud Platform (Production)
# GCP_PROJECT_ID=your-project-id
# GCP_SERVICE_ACCOUNT_KEY=/path/to/service-account-key.json
# SPANNER_INSTANCE_ID=ccl-production
# SPANNER_DATABASE_ID=ccl-main

# Service URLs
ANALYSIS_SERVICE_URL=http://localhost:8001
QUERY_SERVICE_URL=http://localhost:8002
PATTERN_SERVICE_URL=http://localhost:8003
MARKETPLACE_SERVICE_URL=http://localhost:8004
COLLABORATION_SERVICE_URL=http://localhost:8005
WEB_SERVICE_URL=http://localhost:3001

# API Gateway
API_GATEWAY_URL=http://localhost:8000
API_GATEWAY_TIMEOUT=30s
API_RATE_LIMIT=1000
API_RATE_LIMIT_WINDOW=60s

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:8005
NEXT_PUBLIC_GA_TRACKING_ID=
NEXT_PUBLIC_SENTRY_DSN=

# Authentication
JWT_SECRET=dev_jwt_secret_change_me_in_production_to_something_very_secure
JWT_EXPIRY=24h
REFRESH_TOKEN_EXPIRY=7d
SESSION_SECRET=dev_session_secret_change_me_in_production
OAUTH_CLIENT_ID=
OAUTH_CLIENT_SECRET=

# Encryption
ENCRYPTION_KEY=dev_encryption_key_32_bytes_long!!
ENCRYPTION_ALGORITHM=AES-256-GCM

# API Keys (Development - Replace in Production)
API_KEY=dev_api_key_for_testing
ADMIN_API_KEY=dev_admin_api_key_for_testing

# Third-Party Services
STRIPE_SECRET_KEY=sk_test_dummy_key_for_development
STRIPE_WEBHOOK_SECRET=whsec_test_dummy_secret
VERTEX_AI_API_KEY=dummy_key_for_local_dev
VERTEX_AI_PROJECT=
VERTEX_AI_LOCATION=us-central1
OPENAI_API_KEY=
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=
SLACK_WEBHOOK_URL=

# Email Configuration
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>
SMTP_SSL=false

# Observability
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
OTEL_SERVICE_NAME=ccl-platform
OTEL_TRACES_EXPORTER=otlp
OTEL_METRICS_EXPORTER=otlp
OTEL_LOGS_EXPORTER=otlp
JAEGER_UI_URL=http://localhost:16686
GRAFANA_URL=http://localhost:3000
PROMETHEUS_URL=http://localhost:9090

# Monitoring & Alerting
SENTRY_DSN=
SENTRY_ENVIRONMENT=development
PAGERDUTY_API_KEY=
SLACK_ALERT_CHANNEL=

# Feature Flags
ENABLE_DEBUG_LOGGING=true
ENABLE_HOT_RELOAD=true
ENABLE_MOCK_SERVICES=true
ENABLE_DEV_TOOLS=true
ENABLE_PROFILING=false
ENABLE_ANALYTICS=false
ENABLE_RATE_LIMITING=false
ENABLE_CACHE=true
ENABLE_WEBSOCKETS=true
ENABLE_COLLABORATION=true
ENABLE_MARKETPLACE=true
ENABLE_PATTERN_DETECTION=true
ENABLE_AI_FEATURES=true

# Resource Limits
MAX_UPLOAD_SIZE=50MB
MAX_REPOSITORY_SIZE=1GB
MAX_ANALYSIS_TIME=300s
MAX_CONCURRENT_ANALYSES=10
MAX_QUERY_LENGTH=1000
MAX_PATTERN_SIZE=10MB
MAX_WEBSOCKET_CONNECTIONS=1000
MAX_CACHE_SIZE=1GB

# Performance Tuning
WORKER_THREADS=4
CONNECTION_POOL_SIZE=20
QUERY_TIMEOUT=30s
CACHE_TTL=3600
REQUEST_TIMEOUT=30s
IDLE_TIMEOUT=60s

# Development Tools
SWAGGER_ENABLED=true
GRAPHQL_PLAYGROUND_ENABLED=true
DEBUG_ENDPOINTS_ENABLED=true

# Logging
LOG_LEVEL=debug
LOG_FORMAT=json
LOG_OUTPUT=stdout
LOG_FILE_PATH=/var/log/ccl/
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10
LOG_COMPRESS=true

# Security
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization
CORS_CREDENTIALS=true
CSRF_ENABLED=true
CSRF_TOKEN_LENGTH=32
SECURITY_HEADERS_ENABLED=true
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=60000

# Storage
STORAGE_PROVIDER=local
STORAGE_LOCAL_PATH=./data/storage
STORAGE_GCS_BUCKET=
STORAGE_S3_BUCKET=
STORAGE_S3_REGION=
STORAGE_AZURE_CONTAINER=

# Backup & Recovery
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./data/backups

# Deployment
DEPLOYMENT_ENV=local
DEPLOYMENT_REGION=us-central1
DEPLOYMENT_ZONE=us-central1-a
KUBERNETES_NAMESPACE=default
DOCKER_REGISTRY=gcr.io/ccl-platform

# CI/CD
CI_PIPELINE_ID=
CI_COMMIT_SHA=
CI_BRANCH_NAME=
CI_BUILD_NUMBER=
CI_BUILD_URL=

# Miscellaneous
TIMEZONE=UTC
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de,ja,zh
PAGINATION_DEFAULT_LIMIT=20
PAGINATION_MAX_LIMIT=100