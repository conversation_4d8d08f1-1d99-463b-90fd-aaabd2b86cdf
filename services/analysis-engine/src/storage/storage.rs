use crate::models::AnalysisResult;
use crate::storage::gcp_clients;
use anyhow::{Context, Result};
use gcloud_storage::http::objects::download::Range;
use gcloud_storage::http::objects::get::GetObjectRequest;
use gcloud_storage::http::objects::list::ListObjectsRequest;
use gcloud_storage::http::objects::upload::{Media, UploadObjectRequest, UploadType};
use gcloud_storage::client::{Client, ClientConfig};

pub struct StorageOperations {
    storage_client: gcp_clients::StorageClient,
    client: Client,
}

impl StorageOperations {
    pub async fn new(storage_client: gcp_clients::StorageClient) -> Result<Self> {
        let config = ClientConfig::default();
        let client = Client::new(config);
        Ok(Self { storage_client, client })
    }

    pub async fn store_analysis_results(&self, analysis: &AnalysisResult) -> Result<String> {
        let object_name = format!("analysis_results/{}.json", analysis.id);
        let content = serde_json::to_vec(analysis)?;

        // Ensure bucket exists
        let bucket_name = &self.storage_client.bucket_name;
        // Check if bucket exists - gcloud-storage has different API
        // For now, we'll assume the bucket exists (should be created via terraform)

        self.client
            .upload_object(
                &UploadObjectRequest {
                    bucket: bucket_name.clone(),
                    ..Default::default()
                },
                content,
                &UploadType::Simple(Media {
                    name: std::borrow::Cow::Owned(object_name.clone()),
                    content_type: std::borrow::Cow::Borrowed("application/json"),
                    content_length: None,
                }),
            )
            .await
            .context("Failed to upload analysis results")?;

        Ok(format!("gs://{}/{}", bucket_name, object_name))
    }

    pub async fn get_analysis_results(&self, analysis_id: &str) -> Result<AnalysisResult> {
        let object_name = format!("analysis_results/{}.json", analysis_id);
        let bucket_name = &self.storage_client.bucket_name;

        let content = self
            .client
            .download_object(
                &GetObjectRequest {
                    bucket: bucket_name.clone(),
                    object: object_name,
                    ..Default::default()
                },
                &Range::default(),
            )
            .await
            .context("Failed to download analysis results")?;

        let analysis: AnalysisResult = serde_json::from_slice(&content)
            .context("Failed to deserialize analysis results")?;
        Ok(analysis)
    }

    pub async fn store_ast(&self, analysis_id: &str, file_path: &str, ast_data: &[u8]) -> Result<String> {
        let object_name = format!("ast/{}/{}", analysis_id, file_path.replace('/', "_"));
        let bucket_name = &self.storage_client.bucket_name;

        self.client
            .upload_object(
                &UploadObjectRequest {
                    bucket: bucket_name.clone(),
                    ..Default::default()
                },
                ast_data.to_vec(),
                &UploadType::Simple(Media {
                    name: std::borrow::Cow::Owned(object_name.clone()),
                    content_type: std::borrow::Cow::Borrowed("application/octet-stream"),
                    content_length: None,
                }),
            )
            .await
            .context("Failed to upload AST data")?;

        Ok(format!("gs://{}/{}", bucket_name, object_name))
    }

    pub async fn health_check(&self) -> Result<()> {
        let bucket_name = &self.storage_client.bucket_name;
        
        // Check if we can access the bucket by listing with a limit of 1
        self.client
            .list_objects(&ListObjectsRequest {
                bucket: bucket_name.clone(),
                max_results: Some(1),
                ..Default::default()
            })
            .await
            .context("Storage health check failed")?;

        Ok(())
    }
}