use crate::models::{AnalysisResult, ProgressUpdate};
use crate::storage::gcp_clients;
use anyhow::{Context, Result};
use google_cloud_googleapis::pubsub::v1::PubsubMessage;
use google_cloud_pubsub::publisher::{Publisher, PublisherConfig};
use google_cloud_pubsub::topic::Topic;
use std::collections::HashMap;

pub struct PubSubOperations {
    client: gcp_clients::PubSubClient,
    topics: HashMap<String, Topic>,
    publishers: HashMap<String, Publisher>,
    project_id: String,
}

impl PubSubOperations {
    pub async fn new(client: gcp_clients::PubSubClient) -> Result<Self> {
        let project_id = std::env::var("GCP_PROJECT_ID")
            .context("GCP_PROJECT_ID environment variable not set")?;

        let mut topics = HashMap::new();
        let mut publishers = HashMap::new();

        // Create topics and publishers for each event type
        let topic_names = vec![
            "analysis-events",
            "analysis-progress",
            "pattern-detected",
        ];

        for topic_name in topic_names {
            let topic_path = format!("projects/{}/topics/{}", project_id, topic_name);
            let topic = client.topic(&topic_path);
            
            // Configure publisher with batch settings for efficiency
            let publisher_config = PublisherConfig {
                bundle_size: 100,
                flush_interval: std::time::Duration::from_millis(100),
                ..Default::default()
            };
            
            let publisher = topic.new_publisher(Some(publisher_config));
            
            topics.insert(topic_name.to_string(), topic);
            publishers.insert(topic_name.to_string(), publisher);
        }

        Ok(Self {
            client,
            topics,
            publishers,
            project_id,
        })
    }

    pub async fn publish_analysis_event(&self, analysis: &AnalysisResult) -> Result<()> {
        let publisher = self.publishers
            .get("analysis-events")
            .context("Analysis events publisher not found")?;

        let event_data = serde_json::json!({
            "event_type": "analysis.completed",
            "analysis_id": analysis.id,
            "repository_url": analysis.repository_url,
            "status": analysis.status,
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "user_id": analysis.user_id,
            "metrics": {
                "file_count": analysis.file_count,
                "success_rate": analysis.success_rate,
                "duration_seconds": analysis.duration_seconds,
            },
        });

        let message = PubsubMessage {
            data: serde_json::to_vec(&event_data)?,
            attributes: HashMap::from([
                ("event_type".to_string(), "analysis.completed".to_string()),
                ("analysis_id".to_string(), analysis.id.clone()),
            ]),
            ..Default::default()
        };

        let awaiter = publisher.publish(message).await;
        awaiter.get().await.context("Failed to publish analysis event")?;
        
        tracing::debug!(
            "Published analysis event for {} with status {:?}",
            analysis.id,
            analysis.status
        );
        
        Ok(())
    }

    pub async fn publish_progress(&self, progress: &ProgressUpdate) -> Result<()> {
        let publisher = self.publishers
            .get("analysis-progress")
            .context("Progress publisher not found")?;

        let message = PubsubMessage {
            data: serde_json::to_vec(progress)?,
            attributes: HashMap::from([
                ("analysis_id".to_string(), progress.analysis_id.clone()),
                ("stage".to_string(), progress.stage.clone()),
                ("progress".to_string(), progress.progress.to_string()),
            ]),
            ..Default::default()
        };

        let awaiter = publisher.publish(message).await;
        awaiter.get().await.context("Failed to publish progress update")?;
        
        Ok(())
    }

    pub async fn publish_pattern_detected(&self, analysis_id: &str, pattern_type: &str, pattern_count: usize) -> Result<()> {
        let publisher = self.publishers
            .get("pattern-detected")
            .context("Pattern detected publisher not found")?;

        let event_data = serde_json::json!({
            "event_type": "pattern.detected",
            "analysis_id": analysis_id,
            "pattern_type": pattern_type,
            "pattern_count": pattern_count,
            "timestamp": chrono::Utc::now().to_rfc3339(),
        });

        let message = PubsubMessage {
            data: serde_json::to_vec(&event_data)?,
            attributes: HashMap::from([
                ("event_type".to_string(), "pattern.detected".to_string()),
                ("analysis_id".to_string(), analysis_id.to_string()),
                ("pattern_type".to_string(), pattern_type.to_string()),
            ]),
            ..Default::default()
        };

        let awaiter = publisher.publish(message).await;
        awaiter.get().await.context("Failed to publish pattern detected event")?;
        
        tracing::debug!(
            "Published pattern detected event for analysis {} with {} {} patterns",
            analysis_id,
            pattern_count,
            pattern_type
        );
        
        Ok(())
    }

    pub async fn health_check(&self) -> Result<()> {
        // Check if all topics exist
        for (name, topic) in &self.topics {
            let exists = topic.exists(None).await
                .context(format!("Failed to check existence of topic {}", name))?;
            
            if !exists {
                return Err(anyhow::anyhow!("PubSub topic {} does not exist", name));
            }
        }
        Ok(())
    }
}