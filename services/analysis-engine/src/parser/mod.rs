use anyhow::{Context, Result};
use std::path::Path;
use std::collections::HashMap;
use tree_sitter::{Parser, Node};
use crate::models::{FileAnalysis, AstNode, Position, Range, Symbol, SymbolType, FileMetrics, ParseError, ParseErrorType, CodeChunk, ChunkType};
use sha2::{Sha256, Digest};

// Import the language constants from the tree-sitter crates
use tree_sitter_rust::LANGUAGE as RUST_LANGUAGE;
use tree_sitter_python::LANGUAGE as PYTHON_LANGUAGE;
use tree_sitter_javascript::LANGUAGE as JAVASCRIPT_LANGUAGE;
use tree_sitter_typescript::LANGUAGE_TYPESCRIPT;
use tree_sitter_go::LANGUAGE as GO_LANGUAGE;
use tree_sitter_java::LANGUAGE as JAVA_LANGUAGE;
use tree_sitter_c::LANGUAGE as C_LANGUAGE;
use tree_sitter_cpp::LANGUAGE as CPP_LANGUAGE;

pub struct TreeSitterParser {
    parsers: HashMap<String, Parser>,
}

impl TreeSitterParser {
    pub fn new() -> Result<Self> {
        let mut parsers = HashMap::new();
        
        let languages = vec![
            ("rust", RUST_LANGUAGE.into()),
            ("javascript", JAVASCRIPT_LANGUAGE.into()),
            ("typescript", LANGUAGE_TYPESCRIPT.into()),
            ("python", PYTHON_LANGUAGE.into()),
            ("go", GO_LANGUAGE.into()),
            ("java", JAVA_LANGUAGE.into()),
            ("c", C_LANGUAGE.into()),
            ("cpp", CPP_LANGUAGE.into()),
        ];

        for (lang_name, language) in languages {
            let mut parser = Parser::new();
            parser.set_language(&language).context(format!("Failed to load {} grammar", lang_name))?;
            parsers.insert(lang_name.to_string(), parser);
        }

        Ok(Self { parsers })
    }

    pub async fn parse_file(&self, file_path: &Path) -> Result<FileAnalysis, ParseError> {
        let content = tokio::fs::read_to_string(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: e.to_string(),
            position: None,
        })?;
        let language = self.detect_language(file_path)?;
        
        let lang_parser = self.parsers.get(language).ok_or_else(|| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::UnsupportedLanguage,
            message: "Unsupported language".to_string(),
            position: None,
        })?;

        // Parse using the existing parser which already has the language set
        // We'll use the parser directly instead of creating a new one
        let tree = unsafe {
            // Use a raw pointer to bypass the borrow checker temporarily
            // This is safe because we're not modifying the parsers map
            let parser_ptr = lang_parser as *const Parser as *mut Parser;
            (*parser_ptr).parse(&content, None)
        }.ok_or_else(|| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::ParseError,
            message: "Failed to parse file".to_string(),
            position: None,
        })?;
        
        let root_node = tree.root_node();
        let ast = self.build_ast(&root_node, &content);
        let symbols = self.extract_symbols(&root_node, &content);
        let metadata = self.extract_metadata(&content);

        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Extract chunks for embedding
        let chunks = self.extract_chunks(&ast, &content, language);
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: language.to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }

    fn detect_language<'a>(&self, file_path: &'a Path) -> Result<&'a str, ParseError> {
        let extension = file_path.extension().and_then(|s| s.to_str()).unwrap_or("");
        match extension {
            "rs" => Ok("rust"),
            "py" => Ok("python"),
            "js" | "mjs" => Ok("javascript"),
            "ts" | "tsx" => Ok("typescript"),
            "go" => Ok("go"),
            "java" => Ok("java"),
            "c" | "h" => Ok("c"),
            "cpp" | "cc" | "cxx" | "hpp" => Ok("cpp"),
            "cs" => Ok("c#"),
            "php" => Ok("php"),
            "rb" => Ok("ruby"),
            "swift" => Ok("swift"),
            "kt" | "kts" => Ok("kotlin"),
            "scala" => Ok("scala"),
            "hs" => Ok("haskell"),
            "ex" | "exs" => Ok("elixir"),
            "sh" | "bash" => Ok("bash"),
            "html" | "htm" => Ok("html"),
            "css" | "scss" | "sass" => Ok("css"),
            "json" => Ok("json"),
            "yaml" | "yml" => Ok("yaml"),
            "toml" => Ok("toml"),
            "xml" => Ok("xml"),
            "md" | "markdown" => Ok("markdown"),
            "sql" => Ok("sql"),
            _ => Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::UnsupportedLanguage,
                message: format!("Unsupported file extension: {}", extension),
                position: None,
            }),
        }
    }

    fn build_ast(&self, node: &Node, source: &str) -> AstNode {
        let text = node.utf8_text(source.as_bytes()).ok().map(|s| s.to_string());
        let name = node.child_by_field_name("name")
            .and_then(|n| n.utf8_text(source.as_bytes()).ok())
            .map(|s| s.to_string());
        
        AstNode {
            node_type: node.kind().to_string(),
            name,
            range: Range {
                start: self.convert_position(node.start_position()),
                end: self.convert_position(node.end_position()),
            },
            children: node.children(&mut node.walk()).map(|child| self.build_ast(&child, source)).collect(),
            properties: None,
            text,
        }
    }

    fn extract_symbols(&self, node: &Node, source: &str) -> Vec<Symbol> {
        let mut symbols = Vec::new();
        self.traverse_for_symbols(node, source, &mut symbols);
        symbols
    }

    fn traverse_for_symbols(&self, node: &Node, source: &str, symbols: &mut Vec<Symbol>) {
        let symbol_type = match node.kind() {
            "function_item" | "function_declaration" => Some(SymbolType::Function),
            "struct_item" | "class_declaration" => Some(SymbolType::Class),
            "let_declaration" | "const_item" | "variable_declarator" => Some(SymbolType::Variable),
            "trait_item" => Some(SymbolType::Interface),
            _ => None,
        };

        if let Some(st) = symbol_type {
            let name_node = node.child_by_field_name("name").or_else(|| node.child_by_field_name("identifier"));
            if let Some(name_node) = name_node {
                symbols.push(Symbol {
                    name: name_node.utf8_text(source.as_bytes()).unwrap_or("").to_string(),
                    symbol_type: st,
                    range: Range {
                        start: self.convert_position(node.start_position()),
                        end: self.convert_position(node.end_position()),
                    },
                    visibility: None, // Could be extracted based on language
                    signature: None, // Could be extracted for functions
                    documentation: None, // Could be extracted from comments
                });
            }
        }

        for child in node.children(&mut node.walk()) {
            self.traverse_for_symbols(&child, source, symbols);
        }
    }

    fn convert_position(&self, point: tree_sitter::Point) -> Position {
        Position {
            line: point.row as u32,
            column: point.column as u32,
            byte: 0, // Tree-sitter doesn't provide byte offset directly in Point
        }
    }

    fn extract_metadata(&self, content: &str) -> FileMetrics {
        let lines: Vec<&str> = content.lines().collect();
        let total_lines = lines.len() as u32;
        let mut lines_of_code = 0u32;
        let mut comment_lines = 0u32;
        
        for line in &lines {
            let trimmed = line.trim();
            if !trimmed.is_empty() {
                if trimmed.starts_with("//") || trimmed.starts_with("#") || trimmed.starts_with("/*") || trimmed.starts_with("*") {
                    comment_lines += 1;
                } else {
                    lines_of_code += 1;
                }
            }
        }
        
        let comment_ratio = if lines_of_code > 0 {
            Some(comment_lines as f64 / lines_of_code as f64)
        } else {
            None
        };
        
        FileMetrics {
            lines_of_code,
            total_lines: Some(total_lines),
            complexity: 0, // Will be calculated by metrics service
            maintainability_index: 0.0, // Will be calculated by metrics service
            function_count: None,
            class_count: None,
            comment_ratio,
        }
    }
    
    fn extract_chunks(&self, node: &AstNode, source: &str, language: &str) -> Vec<CodeChunk> {
        let mut chunks = Vec::new();
        let mut chunk_counter = 0;
        
        self.extract_chunks_recursive(node, source, language, &mut chunks, &mut chunk_counter);
        chunks
    }
    
    fn extract_chunks_recursive(&self, node: &AstNode, source: &str, language: &str, chunks: &mut Vec<CodeChunk>, counter: &mut usize) {
        // Determine if this node should be a chunk
        let chunk_type = match node.node_type.as_str() {
            "function_item" | "function_declaration" => Some(ChunkType::Function),
            "struct_item" | "class_declaration" => Some(ChunkType::Class),
            "impl_item" | "method_declaration" => Some(ChunkType::Method),
            "block_statement" | "block" => Some(ChunkType::Block),
            "comment" | "line_comment" | "block_comment" => Some(ChunkType::Comment),
            "use_declaration" | "import_statement" | "import_declaration" => Some(ChunkType::Import),
            _ => None,
        };
        
        if let Some(ct) = chunk_type {
            *counter += 1;
            let chunk_id = format!("chunk_{:016x}", counter);
            
            if let Some(ref text) = node.text {
                if text.len() <= 8192 { // Max length from contract
                    chunks.push(CodeChunk {
                        chunk_id,
                        content: text.clone(),
                        range: node.range.clone(),
                        chunk_type: ct,
                        language: Some(language.to_string()),
                        context: None,
                    });
                }
            }
        }
        
        // Recurse into children
        for child in &node.children {
            self.extract_chunks_recursive(child, source, language, chunks, counter);
        }
    }
}
