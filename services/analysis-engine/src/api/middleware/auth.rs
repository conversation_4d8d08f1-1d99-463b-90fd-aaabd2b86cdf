use axum::{
    extract::{Request, State},
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
};
use jsonwebtoken::{decode, decode_header, Algorithm, DecodingKey, Validation};
use serde::{Deserialize, Serialize};
use sha2::{Sha256, Digest};
use crate::api::AppState;
use crate::storage::SpannerOperations;
use gcloud_spanner::statement::Statement;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

#[derive(Debug, Serialize)]
pub struct AuthError {
    pub error: String,
    pub error_code: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,  // Subject (user ID)
    exp: u64,     // Expiration time
    iat: u64,     // Issued at
    aud: String,  // Audience
}

#[derive(Debug)]
#[allow(dead_code)]
struct ApiKeyInfo {
    user_id: String,
    rate_limit: i64,
    expires_at: Option<SystemTime>,
}

pub async fn auth_middleware(
    State(state): State<AppState>,
    headers: HeaderMap,
    mut request: Request,
    next: Next,
) -> Result<Response, impl IntoResponse> {
    // Allow health check endpoints without auth
    let path = request.uri().path();
    if path == "/health" || path == "/ready" || path == "/metrics" {
        return Ok(next.run(request).await);
    }

    // Check for API key in headers
    let api_key = headers
        .get("x-api-key")
        .and_then(|v| v.to_str().ok());

    // Check for Bearer token
    let auth_header = headers
        .get("authorization")
        .and_then(|v| v.to_str().ok());

    let auth_result = match (api_key, auth_header) {
        (Some(key), _) => validate_api_key(key, &state.spanner).await,
        (_, Some(header)) if header.starts_with("Bearer ") => {
            let token = &header[7..];
            validate_jwt_token(token, &state).await
        }
        _ => Err("No authentication provided".to_string()),
    };

    match auth_result {
        Ok(user_id) => {
            // Add user ID to request extensions for downstream handlers
            request.extensions_mut().insert(user_id);
            
            // Log successful authentication
            tracing::debug!("Authentication successful for user");
            
            Ok(next.run(request).await)
        }
        Err(error) => {
            // Log authentication failure
            tracing::warn!("Authentication failed: {}", error);
            
            Err((
                StatusCode::UNAUTHORIZED,
                axum::Json(AuthError {
                    error,
                    error_code: "UNAUTHORIZED".to_string(),
                }),
            ))
        }
    }
}

async fn validate_api_key(key: &str, spanner: &Arc<SpannerOperations>) -> Result<String, String> {
    // Hash the API key for secure storage comparison
    let mut hasher = Sha256::new();
    hasher.update(key.as_bytes());
    let key_hash = format!("{:x}", hasher.finalize());

    // Query Spanner for the API key
    let mut tx = spanner.read_only_transaction()
        .await
        .map_err(|e| format!("Database connection failed: {}", e))?;

    let mut statement = Statement::new(
        "SELECT user_id, rate_limit, expires_at, is_active 
         FROM api_keys 
         WHERE key_hash = @key_hash"
    );
    statement.add_param("key_hash", &key_hash);

    let mut reader = tx.query(statement)
        .await
        .map_err(|e| format!("Failed to query API key: {}", e))?;

    if let Some(row) = reader.next().await
        .map_err(|e| format!("Failed to read API key data: {}", e))? {
        
        // Check if key is active
        let is_active: bool = row.column_by_name("is_active")
            .map_err(|e| format!("Failed to read is_active: {}", e))?;
        
        if !is_active {
            return Err("API key is inactive".to_string());
        }

        // Check expiration
        let expires_at: Option<String> = row.column_by_name("expires_at")
            .ok();
        
        if let Some(ref expires_str) = expires_at {
            let expires_time = chrono::DateTime::parse_from_rfc3339(&expires_str)
                .map_err(|e| format!("Invalid expiration time format: {}", e))?;
            
            if expires_time < chrono::Utc::now() {
                return Err("API key has expired".to_string());
            }
        }

        // Get user ID
        let user_id: String = row.column_by_name("user_id")
            .map_err(|e| format!("Failed to read user_id: {}", e))?;

        // TODO: Implement rate limiting check here
        // let rate_limit: i64 = row.column_by_name("rate_limit")?;

        // Log successful API key validation
        tracing::debug!("API key validated for user: {}", user_id);

        Ok(user_id)
    } else {
        Err("Invalid API key".to_string())
    }
}

async fn validate_jwt_token(token: &str, _state: &AppState) -> Result<String, String> {
    // Decode header to determine key ID if using key rotation
    let _header = decode_header(token)
        .map_err(|e| format!("Invalid JWT header: {}", e))?;

    // Get the public key for verification
    // In production, this would be fetched from a key management service
    // For now, we'll use an environment variable
    let jwt_secret = std::env::var("JWT_SECRET")
        .map_err(|_| "JWT secret not configured".to_string())?;

    let mut validation = Validation::new(Algorithm::HS256);
    validation.set_audience(&["ccl-analysis-engine"]);
    validation.validate_exp = true;
    validation.validate_nbf = true;

    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(jwt_secret.as_bytes()),
        &validation,
    )
    .map_err(|e| match e.kind() {
        jsonwebtoken::errors::ErrorKind::ExpiredSignature => "Token has expired".to_string(),
        jsonwebtoken::errors::ErrorKind::InvalidToken => "Invalid token format".to_string(),
        jsonwebtoken::errors::ErrorKind::InvalidAudience => "Invalid token audience".to_string(),
        _ => format!("Token validation failed: {}", e),
    })?;

    // Additional validation: check if token was issued too far in the past
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let max_token_age = <Duration as DurationExt>::from_days(7).as_secs();
    if now - token_data.claims.iat > max_token_age {
        return Err("Token is too old".to_string());
    }

    // TODO: Check if user exists and is active in the database
    // This would involve querying the users table in Spanner

    tracing::debug!("JWT validated for user: {}", token_data.claims.sub);

    Ok(token_data.claims.sub)
}

// Rate limiting middleware (to be used in conjunction with auth)
#[allow(dead_code)]
pub async fn rate_limit_check(
    _user_id: &str,
    _spanner: &Arc<SpannerOperations>,
) -> Result<(), String> {
    // This would implement rate limiting logic using Redis or Spanner
    // For now, we'll just return Ok
    Ok(())
}

// Helper trait to add Duration::from_days
trait DurationExt {
    fn from_days(days: u64) -> Duration;
}

impl DurationExt for Duration {
    fn from_days(days: u64) -> Duration {
        Duration::from_secs(days * 24 * 60 * 60)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_key_hashing() {
        let key = "test-api-key-12345";
        let mut hasher = Sha256::new();
        hasher.update(key.as_bytes());
        let hash = format!("{:x}", hasher.finalize());
        
        // Verify hash is consistent
        let mut hasher2 = Sha256::new();
        hasher2.update(key.as_bytes());
        let hash2 = format!("{:x}", hasher2.finalize());
        
        assert_eq!(hash, hash2);
        assert_eq!(hash.len(), 64); // SHA256 produces 64 hex characters
    }

    #[tokio::test]
    async fn test_jwt_validation() {
        use jsonwebtoken::{encode, EncodingKey, Header};
        
        std::env::set_var("JWT_SECRET", "test-secret");
        
        let claims = Claims {
            sub: "user123".to_string(),
            exp: (SystemTime::now() + Duration::from_secs(3600))
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            iat: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            aud: "ccl-analysis-engine".to_string(),
        };
        
        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret("test-secret".as_bytes()),
        )
        .unwrap();
        
        // Mock state would be needed for full test
        // This just verifies the JWT encoding/decoding logic
        assert!(!token.is_empty());
    }
}
