pub mod handlers;
pub mod websocket;
pub mod middleware;
pub mod errors;

use std::sync::Arc;
use dashmap::DashMap;
use tokio::sync::broadcast;
use crate::storage::{gcp_clients, SpannerOperations, StorageOperations, PubSubOperations};
use crate::config::ServiceConfig;
use crate::services::analyzer::AnalysisService;
use crate::models::{AnalysisStatus, ProgressUpdate};

#[derive(Clone)]
pub struct AppState {
    pub spanner: Arc<SpannerOperations>,
    pub storage: Arc<StorageOperations>,
    pub pubsub: Arc<PubSubOperations>,
    pub config: Arc<ServiceConfig>,
    pub analysis_service: Arc<AnalysisService>,
    pub active_analyses: Arc<DashMap<String, AnalysisStatus>>,
    pub progress_broadcast: broadcast::Sender<ProgressUpdate>,
}

impl AppState {
    pub async fn new() -> Result<Self, anyhow::Error> {
        let config = Arc::new(ServiceConfig::from_env()?);
        
        // Create the actual GCP clients with proper configuration
        let spanner_client = gcp_clients::create_spanner_client().await?;
        let storage_client = gcp_clients::create_storage_client().await?;
        let pubsub_client = gcp_clients::create_pubsub_client().await?;
        
        // Create operation wrappers
        let spanner = Arc::new(SpannerOperations::new(spanner_client).await?);
        let storage = Arc::new(StorageOperations::new(storage_client).await?);
        let pubsub = Arc::new(PubSubOperations::new(pubsub_client).await?);
        
        // Create broadcast channel for progress updates
        // Buffer size of 1000 should handle high-frequency updates
        let (progress_broadcast, _) = broadcast::channel(1000);
        
        // Create analysis service with the operation wrappers
        let analysis_service = Arc::new(AnalysisService::new(
            spanner.clone(),
            storage.clone(),
            pubsub.clone(),
            config.clone(),
        ).await?);
        
        Ok(Self {
            spanner,
            storage,
            pubsub,
            config,
            analysis_service,
            active_analyses: Arc::new(DashMap::new()),
            progress_broadcast,
        })
    }
}