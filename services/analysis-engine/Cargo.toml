[package]
name = "analysis-engine"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework
axum = { version = "0.8.4", features = ["ws", "macros"] }
tower = "0.5"
tower-http = { version = "0.6.6", features = ["cors", "trace", "compression-full"] }
hyper = { version = "1.0", features = ["full"] }

# Async runtime
tokio = { version = "1.46.1", features = ["full"] }
futures = "0.3"

# Code parsing
tree-sitter = "0.25.6"
tree-sitter-rust = "0.24"
tree-sitter-javascript = "0.23"
tree-sitter-typescript = "0.23"
tree-sitter-python = "0.23"
tree-sitter-go = "0.23"
tree-sitter-java = "0.23"
tree-sitter-c = "0.24"
tree-sitter-cpp = "0.23"

# Serialization
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0"

# Error handling
anyhow = "1.0.98"
thiserror = "1.0"

# Logging
tracing = "0.1.41"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# GCP clients
gcloud-spanner = "1.3.0"
gcloud-storage = "1.1.0"
google-cloud-pubsub = "0.30.0"
google-cloud-gax = "0.19.1"
tonic = "0.13"

# Hashing
sha2 = "0.10"

# WebSocket
tokio-tungstenite = "0.27.0"

# Parallel processing
rayon = "1.10.0"

# Utilities
uuid = { version = "1.17.0", features = ["serde", "v4"] }
chrono = { version = "0.4.41", features = ["serde"] }
dashmap = "6.1.0"
once_cell = "1.19"
rand = "0.8"
google-cloud-auth = "0.16"

# Git operations
git2 = "0.19"

# Authentication
jsonwebtoken = "9.2"

# Metrics
prometheus = "0.13"

# HTTP client
reqwest = { version = "0.11", features = ["json"] }

# Rate limiting
governor = "0.6"
nonzero_ext = "0.3"

# Path operations
walkdir = "2.4"

# Language detection
tokei = "12.1"

# Pattern matching
glob-match = "0.2"

# Configuration
config = "0.13"

# Google Cloud APIs
google-cloud-googleapis = { version = "0.16.1", features = ["pubsub"] }
gcloud-sdk = "0.27.3"

[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }
mockall = "0.12"
tempfile = "3.8"
wiremock = "0.5"

[[bench]]
name = "analysis_bench"
harness = false

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
