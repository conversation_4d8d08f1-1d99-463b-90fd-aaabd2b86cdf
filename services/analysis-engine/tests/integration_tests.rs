#[cfg(test)]
mod integration_tests {
    use axum::http::StatusCode;
    use serde_json::json;

    #[tokio::test]
    async fn test_health_endpoint() {
        let response = reqwest::get("http://localhost:8001/health")
            .await
            .expect("Failed to send request");

        assert_eq!(response.status(), StatusCode::OK);
        
        let body: serde_json::Value = response.json().await.expect("Failed to parse JSON");
        assert_eq!(body["status"], "healthy");
        assert_eq!(body["service"], "analysis-engine");
    }

    #[tokio::test]
    async fn test_supported_languages() {
        let response = reqwest::get("http://localhost:8001/api/v1/languages")
            .await
            .expect("Failed to send request");

        assert_eq!(response.status(), StatusCode::OK);
        
        let body: serde_json::Value = response.json().await.expect("Failed to parse JSON");
        let languages = body["languages"].as_array().expect("Expected array");
        
        // Verify we support 25+ languages as per PRP
        assert!(languages.len() >= 25, "Expected at least 25 languages, got {}", languages.len());
        
        // Verify some key languages are present
        let language_strs: Vec<&str> = languages
            .iter()
            .map(|v| v.as_str().unwrap())
            .collect();
        
        assert!(language_strs.contains(&"rust"));
        assert!(language_strs.contains(&"javascript"));
        assert!(language_strs.contains(&"python"));
        assert!(language_strs.contains(&"go"));
        assert!(language_strs.contains(&"java"));
    }

    #[tokio::test]
    async fn test_analysis_creation() {
        let client = reqwest::Client::new();
        let request_body = json!({
            "repository_url": "https://github.com/example/test.git",
            "branch": "main",
            "include_patterns": ["src/**/*.rs"],
            "exclude_patterns": ["target/**"],
            "analysis_depth": "Full",
            "languages": ["rust"],
            "enable_patterns": true,
            "enable_embeddings": true
        });

        let response = client
            .post("http://localhost:8001/api/v1/analysis")
            .json(&request_body)
            .send()
            .await
            .expect("Failed to send request");

        assert_eq!(response.status(), StatusCode::CREATED);
        
        let body: serde_json::Value = response.json().await.expect("Failed to parse JSON");
        assert!(body["analysis_id"].is_string());
        assert_eq!(body["status"], "Pending");
        assert!(body["progress_url"].is_string());
    }

    #[tokio::test]
    async fn test_concurrent_analysis_limit() {
        // Test that we can handle 50+ concurrent analyses as per PRP
        let client = reqwest::Client::new();
        let mut handles = vec![];

        for i in 0..55 {
            let client = client.clone();
            let handle = tokio::spawn(async move {
                let request_body = json!({
                    "repository_url": format!("https://github.com/example/test{}.git", i),
                    "branch": "main",
                    "include_patterns": ["src/**/*.rs"],
                    "exclude_patterns": ["target/**"],
                    "analysis_depth": "Shallow",
                    "languages": ["rust"],
                    "enable_patterns": false,
                    "enable_embeddings": false
                });

                client
                    .post("http://localhost:8001/api/v1/analysis")
                    .json(&request_body)
                    .send()
                    .await
            });
            handles.push(handle);
        }

        let results = futures::future::join_all(handles).await;
        
        let successful_count = results
            .iter()
            .filter(|r| r.is_ok() && r.as_ref().unwrap().is_ok())
            .count();
        
        // At least 50 should succeed
        assert!(successful_count >= 50, "Expected at least 50 successful requests, got {}", successful_count);
    }

    #[tokio::test]
    async fn test_websocket_connection() {
        use tokio_tungstenite::{connect_async, tungstenite::Message};

        let url = "ws://localhost:8001/ws/analysis/test-id";
        let (ws_stream, _) = connect_async(url)
            .await
            .expect("Failed to connect to WebSocket");

        let (mut write, mut read) = ws_stream.split();
        
        // Should receive initial status message
        if let Some(Ok(msg)) = read.next().await {
            match msg {
                Message::Text(text) => {
                    let json: serde_json::Value = serde_json::from_str(&text)
                        .expect("Failed to parse WebSocket message");
                    assert!(json["progress"].is_number());
                    assert!(json["stage"].is_string());
                }
                _ => panic!("Expected text message"),
            }
        }
    }
}