# Query Intelligence Service Environment Variables

# Service Configuration
QUERY_INTELLIGENCE_SERVICE_NAME=query-intelligence
QUERY_INTELLIGENCE_SERVICE_VERSION=0.1.0
QUERY_INTELLIGENCE_ENVIRONMENT=development
QUERY_INTELLIGENCE_DEBUG=true

# API Configuration
QUERY_INTELLIGENCE_API_HOST=0.0.0.0
QUERY_INTELLIGENCE_API_PORT=8001
QUERY_INTELLIGENCE_API_WORKERS=4
QUERY_INTELLIGENCE_CORS_ORIGINS=["*"]

# Google Cloud Configuration
QUERY_INTELLIGENCE_GCP_PROJECT_ID=ccl-development
QUERY_INTELLIGENCE_GCP_LOCATION=us-central1

# Vertex AI Configuration
QUERY_INTELLIGENCE_VERTEX_AI_MODEL=gemini-2.5-flash
QUERY_INTELLIGENCE_VERTEX_AI_TEMPERATURE=0.3
QUERY_INTELLIGENCE_VERTEX_AI_MAX_OUTPUT_TOKENS=2048
QUERY_INTELLIGENCE_VERTEX_AI_TIMEOUT_SECONDS=30
QUERY_INTELLIGENCE_EMBEDDING_MODEL=text-embedding-004
QUERY_INTELLIGENCE_EMBEDDING_DIMENSIONS=768

# Vector Search Configuration (optional in development)
# QUERY_INTELLIGENCE_VECTOR_SEARCH_INDEX_ENDPOINT=projects/PROJECT_ID/locations/LOCATION/indexEndpoints/INDEX_ENDPOINT_ID
# QUERY_INTELLIGENCE_VECTOR_SEARCH_DEPLOYED_INDEX_ID=DEPLOYED_INDEX_ID
QUERY_INTELLIGENCE_VECTOR_SEARCH_SIMILARITY_THRESHOLD=0.7
QUERY_INTELLIGENCE_VECTOR_SEARCH_TOP_K=20

# Redis Configuration
QUERY_INTELLIGENCE_REDIS_URL=redis://localhost:6379/0
# QUERY_INTELLIGENCE_REDIS_PASSWORD=
QUERY_INTELLIGENCE_REDIS_MAX_CONNECTIONS=10
QUERY_INTELLIGENCE_CACHE_TTL_SECONDS=3600

# Spanner Configuration
QUERY_INTELLIGENCE_SPANNER_INSTANCE=ccl-dev
QUERY_INTELLIGENCE_SPANNER_DATABASE=ccl-dev-db

# Pub/Sub Configuration
QUERY_INTELLIGENCE_PUBSUB_SUBSCRIPTION=repository-analysis-complete-sub

# Security Configuration
QUERY_INTELLIGENCE_API_KEY_HEADER=X-API-Key
QUERY_INTELLIGENCE_ENABLE_API_KEY_AUTH=false
QUERY_INTELLIGENCE_TRUSTED_HOSTS=["*"]

# Rate Limiting
QUERY_INTELLIGENCE_RATE_LIMIT_ENABLED=true
QUERY_INTELLIGENCE_RATE_LIMIT_REQUESTS_PER_MINUTE=60

# Monitoring Configuration
QUERY_INTELLIGENCE_ENABLE_TELEMETRY=true
QUERY_INTELLIGENCE_OTLP_ENDPOINT=http://localhost:4317
QUERY_INTELLIGENCE_OTLP_INSECURE=true
QUERY_INTELLIGENCE_SAMPLING_RATIO=0.1
QUERY_INTELLIGENCE_LOG_LEVEL=INFO

# Query Processing Configuration
QUERY_INTELLIGENCE_MAX_QUERY_LENGTH=1000
QUERY_INTELLIGENCE_MAX_CONTEXT_CHUNKS=10
QUERY_INTELLIGENCE_MAX_FOLLOW_UP_QUESTIONS=3
QUERY_INTELLIGENCE_CONFIDENCE_THRESHOLD=0.7
QUERY_INTELLIGENCE_QUERY_TIMEOUT_SECONDS=30

# Code Chunking Configuration
QUERY_INTELLIGENCE_CHUNK_SIZE=512
QUERY_INTELLIGENCE_CHUNK_OVERLAP=50
QUERY_INTELLIGENCE_MAX_CHUNK_TOKENS=8192

# Performance Configuration
QUERY_INTELLIGENCE_ENABLE_QUERY_CACHE=true
QUERY_INTELLIGENCE_ENABLE_EMBEDDING_CACHE=true
QUERY_INTELLIGENCE_BATCH_SIZE=10
QUERY_INTELLIGENCE_MAX_CONCURRENT_QUERIES=50

# Google Application Credentials (for local development)
# Set this to the path of your service account key file
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json