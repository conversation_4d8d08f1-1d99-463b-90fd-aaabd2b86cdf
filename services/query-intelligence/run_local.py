#!/usr/bin/env python
"""
Script to run the Query Intelligence service locally
"""

import os
import sys

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

if __name__ == "__main__":
    import uvicorn
    from query_intelligence.config import get_settings
    
    settings = get_settings()
    
    # Run the service
    uvicorn.run(
        "query_intelligence.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )