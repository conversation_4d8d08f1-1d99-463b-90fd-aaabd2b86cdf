"""
Main FastAPI application for Query Intelligence service
"""

import asyncio
import time
from contextlib import asynccontextmanager
from typing import Any, Dict

import structlog
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from opentelemetry.instrumentation.fastapi import Fast<PERSON>IInstrumentor
from starlette.exceptions import HTTPException as StarletteHTTPException

from query_intelligence import __service__, __version__
from query_intelligence.api.v1 import router as v1_router
from query_intelligence.config import get_settings
from query_intelligence.exceptions import QueryIntelligenceHTTPError
from query_intelligence.models import HealthResponse, LivenessResponse, ServiceStatus

# Initialize structured logging
logger = structlog.get_logger()
settings = get_settings()

# Track service start time
SERVICE_START_TIME = time.time()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Manage application lifecycle
    """
    # Startup
    logger.info(
        "Starting Query Intelligence service",
        service=__service__,
        version=__version__,
        environment=settings.environment
    )
    
    # Initialize telemetry if enabled
    if settings.enable_telemetry:
        try:
            # Try to import from infrastructure package
            import sys
            import os
            # Add parent directories to path for infrastructure access
            sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../')))
            
            from infrastructure.monitoring.instrumentation.python_instrumentation import (
                TelemetryConfig,
                init_telemetry,
            )
            
            telemetry_config = TelemetryConfig(
                service_name=settings.service_name,
                service_version=settings.service_version,
                environment=settings.environment,
                otlp_endpoint=settings.otlp_endpoint,
                sampling_ratio=settings.sampling_ratio,
                log_level=settings.log_level,
                insecure=settings.otlp_insecure,
            )
            init_telemetry(telemetry_config)
            logger.info("Telemetry initialized")
        except ImportError:
            logger.warning("Telemetry instrumentation not available, continuing without telemetry")
    
    # Initialize service dependencies
    # TODO: Initialize Vertex AI, Redis, Spanner clients
    
    yield
    
    # Shutdown
    logger.info("Shutting down Query Intelligence service")
    
    # Clean up resources
    # TODO: Close client connections
    
    if settings.enable_telemetry:
        try:
            from infrastructure.monitoring.instrumentation.python_instrumentation import (
                shutdown_telemetry,
            )
            shutdown_telemetry()
        except ImportError:
            pass


# Create FastAPI application
app = FastAPI(
    title="Query Intelligence Service",
    description="Natural language processing service for code queries",
    version=__version__,
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    openapi_url="/openapi.json" if settings.debug else None,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.trusted_hosts
)

app.add_middleware(
    GZipMiddleware,
    minimum_size=1000
)

# Instrument with OpenTelemetry
if settings.enable_telemetry:
    FastAPIInstrumentor.instrument_app(app)


# Exception handlers
@app.exception_handler(QueryIntelligenceHTTPError)
async def query_intelligence_exception_handler(
    request: Request,
    exc: QueryIntelligenceHTTPError
) -> JSONResponse:
    """Handle Query Intelligence specific exceptions"""
    logger.error(
        "Query Intelligence error",
        error_code=exc.detail["error_code"],
        message=exc.detail["message"],
        path=request.url.path,
        method=request.method
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=exc.detail,
        headers=exc.headers
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(
    request: Request,
    exc: RequestValidationError
) -> JSONResponse:
    """Handle request validation errors"""
    logger.warning(
        "Request validation error",
        errors=exc.errors(),
        path=request.url.path,
        method=request.method
    )
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error_code": "VALIDATION_ERROR",
            "message": "Request validation failed",
            "details": exc.errors()
        }
    )


@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(
    request: Request,
    exc: StarletteHTTPException
) -> JSONResponse:
    """Handle generic HTTP exceptions"""
    logger.error(
        "HTTP exception",
        status_code=exc.status_code,
        detail=exc.detail,
        path=request.url.path,
        method=request.method
    )
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error_code": "HTTP_ERROR",
            "message": exc.detail,
            "details": {}
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(
    request: Request,
    exc: Exception
) -> JSONResponse:
    """Handle unexpected exceptions"""
    logger.exception(
        "Unexpected error",
        exc_info=exc,
        path=request.url.path,
        method=request.method
    )
    
    # Don't expose internal errors in production
    if settings.is_production:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "error_code": "INTERNAL_ERROR",
                "message": "An unexpected error occurred",
                "details": {}
            }
        )
    else:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "error_code": "INTERNAL_ERROR",
                "message": str(exc),
                "details": {"type": type(exc).__name__}
            }
        )


# Health check endpoints
@app.get(
    "/health",
    response_model=HealthResponse,
    tags=["health"],
    summary="Health check",
    description="Get detailed health status of the service and its dependencies"
)
async def health_check() -> HealthResponse:
    """
    Comprehensive health check endpoint
    """
    dependencies: Dict[str, Any] = {}
    overall_status = ServiceStatus.HEALTHY
    
    # Check Redis
    try:
        # TODO: Implement Redis health check
        dependencies["redis"] = {
            "name": "Redis",
            "status": ServiceStatus.HEALTHY,
            "latency_ms": 1.2,
            "last_check": time.time()
        }
    except Exception as e:
        dependencies["redis"] = {
            "name": "Redis",
            "status": ServiceStatus.UNHEALTHY,
            "error": str(e),
            "last_check": time.time()
        }
        overall_status = ServiceStatus.DEGRADED
    
    # Check Vertex AI
    try:
        # TODO: Implement Vertex AI health check
        dependencies["vertex_ai"] = {
            "name": "Vertex AI",
            "status": ServiceStatus.HEALTHY,
            "latency_ms": 15.3,
            "last_check": time.time()
        }
    except Exception as e:
        dependencies["vertex_ai"] = {
            "name": "Vertex AI",
            "status": ServiceStatus.UNHEALTHY,
            "error": str(e),
            "last_check": time.time()
        }
        overall_status = ServiceStatus.DEGRADED
    
    # Check Spanner
    try:
        # TODO: Implement Spanner health check
        dependencies["spanner"] = {
            "name": "Spanner",
            "status": ServiceStatus.HEALTHY,
            "latency_ms": 8.7,
            "last_check": time.time()
        }
    except Exception as e:
        dependencies["spanner"] = {
            "name": "Spanner",
            "status": ServiceStatus.UNHEALTHY,
            "error": str(e),
            "last_check": time.time()
        }
        overall_status = ServiceStatus.DEGRADED
    
    # Calculate metrics
    uptime_seconds = time.time() - SERVICE_START_TIME
    
    # TODO: Get actual metrics from monitoring
    metrics = {
        "queries_per_minute": 0.0,
        "average_query_latency_ms": 0.0,
        "cache_hit_rate": 0.0,
        "active_connections": 0,
    }
    
    return HealthResponse(
        status=overall_status,
        service=settings.service_name,
        version=settings.service_version,
        environment=settings.environment,
        uptime_seconds=uptime_seconds,
        dependencies=dependencies,
        metrics=metrics
    )


@app.get(
    "/liveness",
    response_model=LivenessResponse,
    tags=["health"],
    summary="Liveness check",
    description="Simple liveness check for container orchestration"
)
async def liveness_check() -> LivenessResponse:
    """
    Simple liveness check endpoint
    """
    return LivenessResponse(alive=True)


@app.get(
    "/readiness",
    tags=["health"],
    summary="Readiness check",
    description="Check if service is ready to handle requests"
)
async def readiness_check():
    """
    Readiness check endpoint
    """
    checks = {
        "database": False,
        "cache": False,
        "vertex_ai": False,
    }
    
    # TODO: Implement actual readiness checks
    # For now, assume all services are ready
    checks = {k: True for k in checks}
    
    ready = all(checks.values())
    
    if not ready:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "ready": False,
                "checks": checks,
                "message": "Service is not ready"
            }
        )
    
    return {
        "ready": True,
        "checks": checks,
        "message": "Service is ready"
    }


# Include API routers
app.include_router(
    v1_router,
    prefix=settings.api_v1_prefix,
    tags=["v1"]
)


# Root endpoint
@app.get(
    "/",
    tags=["root"],
    summary="Root endpoint",
    description="Basic service information"
)
async def root():
    """
    Root endpoint with basic service information
    """
    return {
        "service": settings.service_name,
        "version": settings.service_version,
        "environment": settings.environment,
        "api_version": "v1",
        "docs_url": "/docs" if settings.debug else None,
    }


# Custom middleware for request tracking
@app.middleware("http")
async def track_requests(request: Request, call_next):
    """
    Track request metrics and add request ID
    """
    start_time = time.time()
    
    # Generate request ID
    import uuid
    request_id = str(uuid.uuid4())
    
    # Log request
    logger.info(
        "Request started",
        request_id=request_id,
        method=request.method,
        path=request.url.path,
        client_host=request.client.host if request.client else None
    )
    
    # Process request
    response = await call_next(request)
    
    # Calculate duration
    duration_ms = (time.time() - start_time) * 1000
    
    # Add headers
    response.headers["X-Request-ID"] = request_id
    response.headers["X-Response-Time-MS"] = str(duration_ms)
    
    # Log response
    logger.info(
        "Request completed",
        request_id=request_id,
        method=request.method,
        path=request.url.path,
        status_code=response.status_code,
        duration_ms=duration_ms
    )
    
    return response


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "query_intelligence.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        workers=1 if settings.debug else settings.api_workers,
        log_level=settings.log_level.lower(),
    )