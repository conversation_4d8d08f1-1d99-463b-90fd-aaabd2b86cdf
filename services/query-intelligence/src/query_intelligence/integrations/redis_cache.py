"""
Redis cache integration with async support
"""

import json
import pickle
from typing import Any, Optional, Union

import redis.asyncio as redis
import structlog
from redis.asyncio.connection import ConnectionPool

from query_intelligence.config import get_settings
from query_intelligence.exceptions import CacheError

logger = structlog.get_logger()
settings = get_settings()


class RedisCache:
    """
    Async Redis cache client with connection pooling
    """
    
    def __init__(
        self,
        url: Optional[str] = None,
        max_connections: Optional[int] = None,
        decode_responses: bool = False,
    ):
        self.url = url or settings.redis_url_with_password
        self.max_connections = max_connections or settings.redis_max_connections
        self.decode_responses = decode_responses
        
        # Create connection pool
        self.pool = ConnectionPool.from_url(
            self.url,
            max_connections=self.max_connections,
            decode_responses=self.decode_responses,
            socket_timeout=settings.redis_socket_timeout,
            socket_connect_timeout=settings.redis_socket_connect_timeout,
        )
        
        # Create Redis client
        self.client = redis.Redis(connection_pool=self.pool)
        
        logger.info(
            "Redis cache initialized",
            max_connections=self.max_connections
        )
    
    async def get(
        self,
        key: str,
        deserialize: bool = True,
    ) -> Optional[Any]:
        """
        Get value from cache
        
        Args:
            key: Cache key
            deserialize: Whether to deserialize the value
            
        Returns:
            Cached value or None if not found
        """
        try:
            value = await self.client.get(key)
            
            if value is None:
                return None
            
            if deserialize:
                try:
                    # Try JSON deserialization first
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    # Fall back to pickle
                    return pickle.loads(value)
            
            return value
            
        except Exception as e:
            logger.error(
                "Cache get failed",
                key=key,
                error=str(e)
            )
            raise CacheError(operation="get", reason=str(e))
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        serialize: bool = True,
    ) -> bool:
        """
        Set value in cache
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            serialize: Whether to serialize the value
            
        Returns:
            True if successful
        """
        try:
            ttl = ttl or settings.cache_ttl_seconds
            
            if serialize:
                try:
                    # Try JSON serialization first
                    serialized_value = json.dumps(value)
                except (TypeError, ValueError):
                    # Fall back to pickle
                    serialized_value = pickle.dumps(value)
            else:
                serialized_value = value
            
            if ttl > 0:
                await self.client.setex(key, ttl, serialized_value)
            else:
                await self.client.set(key, serialized_value)
            
            return True
            
        except Exception as e:
            logger.error(
                "Cache set failed",
                key=key,
                error=str(e)
            )
            raise CacheError(operation="set", reason=str(e))
    
    async def delete(self, key: str) -> bool:
        """
        Delete key from cache
        
        Args:
            key: Cache key
            
        Returns:
            True if key was deleted
        """
        try:
            result = await self.client.delete(key)
            return bool(result)
        except Exception as e:
            logger.error(
                "Cache delete failed",
                key=key,
                error=str(e)
            )
            raise CacheError(operation="delete", reason=str(e))
    
    async def exists(self, key: str) -> bool:
        """
        Check if key exists in cache
        
        Args:
            key: Cache key
            
        Returns:
            True if key exists
        """
        try:
            return bool(await self.client.exists(key))
        except Exception as e:
            logger.error(
                "Cache exists check failed",
                key=key,
                error=str(e)
            )
            raise CacheError(operation="exists", reason=str(e))
    
    async def expire(self, key: str, ttl: int) -> bool:
        """
        Set expiration on existing key
        
        Args:
            key: Cache key
            ttl: Time to live in seconds
            
        Returns:
            True if expiration was set
        """
        try:
            return bool(await self.client.expire(key, ttl))
        except Exception as e:
            logger.error(
                "Cache expire failed",
                key=key,
                error=str(e)
            )
            raise CacheError(operation="expire", reason=str(e))
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """
        Increment counter
        
        Args:
            key: Counter key
            amount: Increment amount
            
        Returns:
            New counter value
        """
        try:
            return await self.client.incr(key, amount)
        except Exception as e:
            logger.error(
                "Cache incr failed",
                key=key,
                error=str(e)
            )
            raise CacheError(operation="incr", reason=str(e))
    
    async def get_many(self, keys: list[str]) -> dict[str, Any]:
        """
        Get multiple values from cache
        
        Args:
            keys: List of cache keys
            
        Returns:
            Dict of key-value pairs
        """
        try:
            if not keys:
                return {}
            
            values = await self.client.mget(keys)
            result = {}
            
            for key, value in zip(keys, values):
                if value is not None:
                    try:
                        result[key] = json.loads(value)
                    except (json.JSONDecodeError, TypeError):
                        result[key] = pickle.loads(value)
            
            return result
            
        except Exception as e:
            logger.error(
                "Cache mget failed",
                keys_count=len(keys),
                error=str(e)
            )
            raise CacheError(operation="mget", reason=str(e))
    
    async def set_many(
        self,
        mapping: dict[str, Any],
        ttl: Optional[int] = None,
    ) -> bool:
        """
        Set multiple values in cache
        
        Args:
            mapping: Dict of key-value pairs
            ttl: Time to live in seconds
            
        Returns:
            True if successful
        """
        try:
            if not mapping:
                return True
            
            # Serialize values
            serialized_mapping = {}
            for key, value in mapping.items():
                try:
                    serialized_mapping[key] = json.dumps(value)
                except (TypeError, ValueError):
                    serialized_mapping[key] = pickle.dumps(value)
            
            # Use pipeline for atomic operation
            async with self.client.pipeline(transaction=True) as pipe:
                for key, value in serialized_mapping.items():
                    if ttl and ttl > 0:
                        pipe.setex(key, ttl, value)
                    else:
                        pipe.set(key, value)
                
                await pipe.execute()
            
            return True
            
        except Exception as e:
            logger.error(
                "Cache mset failed",
                keys_count=len(mapping),
                error=str(e)
            )
            raise CacheError(operation="mset", reason=str(e))
    
    async def clear_pattern(self, pattern: str) -> int:
        """
        Delete all keys matching pattern
        
        Args:
            pattern: Key pattern (e.g., "query:*")
            
        Returns:
            Number of keys deleted
        """
        try:
            # Use SCAN to avoid blocking
            deleted_count = 0
            cursor = 0
            
            while True:
                cursor, keys = await self.client.scan(
                    cursor,
                    match=pattern,
                    count=100
                )
                
                if keys:
                    deleted_count += await self.client.delete(*keys)
                
                if cursor == 0:
                    break
            
            return deleted_count
            
        except Exception as e:
            logger.error(
                "Cache clear pattern failed",
                pattern=pattern,
                error=str(e)
            )
            raise CacheError(operation="clear_pattern", reason=str(e))
    
    async def ping(self) -> bool:
        """
        Check Redis connection
        
        Returns:
            True if connected
        """
        try:
            return await self.client.ping()
        except Exception:
            return False
    
    async def close(self):
        """Close Redis connection"""
        await self.client.close()
        await self.pool.disconnect()
        logger.info("Redis cache connection closed")
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()


class CacheKeyBuilder:
    """
    Utility class for building cache keys
    """
    
    QUERY_PREFIX = "query"
    EMBEDDING_PREFIX = "embedding"
    RESULT_PREFIX = "result"
    INDEX_PREFIX = "index"
    SESSION_PREFIX = "session"
    
    @staticmethod
    def query_key(query: str, repository_id: str) -> str:
        """Build cache key for query"""
        # Use hash of query to avoid long keys
        import hashlib
        query_hash = hashlib.sha256(query.encode()).hexdigest()[:16]
        return f"{CacheKeyBuilder.QUERY_PREFIX}:{repository_id}:{query_hash}"
    
    @staticmethod
    def embedding_key(text: str, model: str) -> str:
        """Build cache key for embedding"""
        import hashlib
        text_hash = hashlib.sha256(text.encode()).hexdigest()[:16]
        return f"{CacheKeyBuilder.EMBEDDING_PREFIX}:{model}:{text_hash}"
    
    @staticmethod
    def result_key(query_id: str) -> str:
        """Build cache key for query result"""
        return f"{CacheKeyBuilder.RESULT_PREFIX}:{query_id}"
    
    @staticmethod
    def index_key(repository_id: str, chunk_id: str) -> str:
        """Build cache key for indexed chunk"""
        return f"{CacheKeyBuilder.INDEX_PREFIX}:{repository_id}:{chunk_id}"
    
    @staticmethod
    def session_key(session_id: str) -> str:
        """Build cache key for session data"""
        return f"{CacheKeyBuilder.SESSION_PREFIX}:{session_id}"


# Singleton instance
_redis_cache: Optional[RedisCache] = None


async def get_redis_cache() -> RedisCache:
    """
    Get or create Redis cache singleton
    
    Returns:
        RedisCache instance
    """
    global _redis_cache
    if _redis_cache is None:
        _redis_cache = RedisCache()
    return _redis_cache