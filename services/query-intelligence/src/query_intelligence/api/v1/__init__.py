"""
API v1 router aggregation
"""

from fastapi import APIRouter

from .query import router as query_router
from .feedback import router as feedback_router
from .suggestions import router as suggestions_router
from .indexing import router as indexing_router

# Create main v1 router
router = APIRouter()

# Include sub-routers
router.include_router(query_router, prefix="/query", tags=["query"])
router.include_router(feedback_router, prefix="/feedback", tags=["feedback"])
router.include_router(suggestions_router, prefix="/suggestions", tags=["suggestions"])
router.include_router(indexing_router, prefix="/index", tags=["indexing"])