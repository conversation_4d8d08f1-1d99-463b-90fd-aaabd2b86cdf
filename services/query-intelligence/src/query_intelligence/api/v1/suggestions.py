"""
Query suggestions API endpoints
"""

from typing import Optional

import structlog
from fastapi import APIRouter, Query, status

from query_intelligence.config import get_settings
from query_intelligence.integrations.vertex_ai import get_vertex_ai_client
from query_intelligence.models import QuerySuggestion, QuerySuggestionsResponse

logger = structlog.get_logger()
router = APIRouter()
settings = get_settings()


@router.get(
    "/",
    response_model=QuerySuggestionsResponse,
    status_code=status.HTTP_200_OK,
    summary="Get query suggestions",
    description="Get suggested queries based on repository content or context",
    responses={
        200: {"description": "Suggestions retrieved successfully"},
        500: {"description": "Internal server error"},
    }
)
async def get_suggestions(
    repository_id: str = Query(
        ...,
        description="Repository to get suggestions for",
        pattern="^repo_[a-zA-Z0-9]{16}$"
    ),
    based_on: Optional[str] = Query(
        None,
        description="Base suggestions on this context (e.g., file path, previous query)"
    ),
    count: int = Query(
        5,
        ge=1,
        le=10,
        description="Number of suggestions to return"
    )
) -> QuerySuggestionsResponse:
    """
    Get query suggestions for a repository
    
    Args:
        repository_id: Repository identifier
        based_on: Optional context to base suggestions on
        count: Number of suggestions to return
        
    Returns:
        List of query suggestions
    """
    try:
        logger.info(
            "Generating query suggestions",
            repository_id=repository_id,
            based_on=based_on,
            count=count
        )
        
        # Generate suggestions using Vertex AI
        vertex_ai = get_vertex_ai_client()
        
        prompt = f"""
        Generate {count} useful natural language queries that a developer might ask about a codebase.
        Repository: {repository_id}
        """
        
        if based_on:
            prompt += f"\nBase suggestions on this context: {based_on}"
        else:
            prompt += "\nInclude a variety of query types: architecture, functionality, patterns, issues, etc."
        
        prompt += """
        
        For each suggestion, provide:
        1. The query text
        2. A category (architecture, functionality, patterns, security, performance, documentation, testing, debugging)
        3. A relevance score (0.0 to 1.0)
        
        Format as JSON array with objects containing: suggestion, category, relevance_score
        """
        
        response = await vertex_ai.generate_content_async(prompt)
        
        # Parse response
        suggestions = []
        try:
            import json
            import re
            
            # Find JSON array in response
            match = re.search(r'\[.*?\]', response, re.DOTALL)
            if match:
                suggestions_data = json.loads(match.group())
                
                for item in suggestions_data[:count]:
                    suggestion = QuerySuggestion(
                        suggestion=item.get("suggestion", ""),
                        category=item.get("category", "general"),
                        relevance_score=float(item.get("relevance_score", 0.8))
                    )
                    suggestions.append(suggestion)
        except Exception as e:
            logger.warning(
                "Failed to parse suggestions",
                error=str(e)
            )
            
            # Fallback suggestions
            default_suggestions = [
                ("How does the authentication system work?", "architecture", 0.9),
                ("What are the main API endpoints?", "functionality", 0.9),
                ("Are there any security vulnerabilities?", "security", 0.85),
                ("What design patterns are used?", "patterns", 0.8),
                ("How is error handling implemented?", "functionality", 0.8),
            ]
            
            for text, category, score in default_suggestions[:count]:
                suggestions.append(QuerySuggestion(
                    suggestion=text,
                    category=category,
                    relevance_score=score
                ))
        
        return QuerySuggestionsResponse(
            suggestions=suggestions,
            based_on=based_on
        )
        
    except Exception as e:
        logger.error(
            "Error generating suggestions",
            repository_id=repository_id,
            error=str(e),
            exc_info=e
        )
        
        # Return default suggestions on error
        default_suggestions = [
            QuerySuggestion(
                suggestion="How does the authentication system work?",
                category="architecture",
                relevance_score=0.9
            ),
            QuerySuggestion(
                suggestion="What are the main API endpoints?",
                category="functionality",
                relevance_score=0.9
            ),
            QuerySuggestion(
                suggestion="Are there any security vulnerabilities?",
                category="security",
                relevance_score=0.85
            ),
        ]
        
        return QuerySuggestionsResponse(
            suggestions=default_suggestions[:count],
            based_on=based_on
        )