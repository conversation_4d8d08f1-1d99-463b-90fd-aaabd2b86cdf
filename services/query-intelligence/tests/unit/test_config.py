"""
Unit tests for configuration management
"""

import os
import pytest

from query_intelligence.config import Settings, get_settings


class TestSettings:
    """Tests for Settings configuration"""
    
    def test_default_settings(self):
        """Test default settings values"""
        settings = Settings(
            gcp_project_id="test-project",
            spanner_instance="test-instance",
            spanner_database="test-database"
        )
        
        assert settings.service_name == "query-intelligence"
        assert settings.environment == "development"
        assert settings.api_port == 8001
        assert settings.vertex_ai_model == "gemini-2.5-flash"
        assert settings.embedding_model == "text-embedding-004"
        assert settings.embedding_dimensions == 768
    
    def test_environment_validation(self):
        """Test environment validation"""
        with pytest.raises(ValueError):
            Settings(
                gcp_project_id="test-project",
                spanner_instance="test-instance",
                spanner_database="test-database",
                environment="invalid"
            )
    
    def test_cors_origins_parsing(self):
        """Test CORS origins parsing from string"""
        settings = Settings(
            gcp_project_id="test-project",
            spanner_instance="test-instance",
            spanner_database="test-database",
            cors_origins="http://localhost:3000,http://localhost:8080"
        )
        
        assert settings.cors_origins == ["http://localhost:3000", "http://localhost:8080"]
    
    def test_redis_url_with_password(self):
        """Test Redis URL with password"""
        settings = Settings(
            gcp_project_id="test-project",
            spanner_instance="test-instance",
            spanner_database="test-database",
            redis_url="redis://localhost:6379/0",
            redis_password="secret123"
        )
        
        assert settings.redis_url_with_password == "redis://:secret123@localhost:6379/0"
    
    def test_is_production(self):
        """Test is_production property"""
        settings = Settings(
            gcp_project_id="test-project",
            spanner_instance="test-instance",
            spanner_database="test-database",
            environment="production"
        )
        
        assert settings.is_production is True
        assert settings.is_development is False
    
    def test_port_validation(self):
        """Test port range validation"""
        with pytest.raises(ValueError):
            Settings(
                gcp_project_id="test-project",
                spanner_instance="test-instance",
                spanner_database="test-database",
                api_port=70000  # Out of range
            )
    
    def test_float_bounds(self):
        """Test float value bounds"""
        settings = Settings(
            gcp_project_id="test-project",
            spanner_instance="test-instance",
            spanner_database="test-database",
            vertex_ai_temperature=0.5,
            confidence_threshold=0.8,
            sampling_ratio=0.1
        )
        
        assert 0.0 <= settings.vertex_ai_temperature <= 1.0
        assert 0.0 <= settings.confidence_threshold <= 1.0
        assert 0.0 <= settings.sampling_ratio <= 1.0


class TestGetSettings:
    """Tests for get_settings function"""
    
    def test_settings_singleton(self):
        """Test that get_settings returns the same instance"""
        settings1 = get_settings()
        settings2 = get_settings()
        
        # Should be the same object (singleton)
        assert settings1 is settings2
    
    def test_settings_caching(self):
        """Test that settings are cached"""
        # Clear the cache first
        get_settings.cache_clear()
        
        # Create settings with environment variable
        os.environ["QUERY_INTELLIGENCE_SERVICE_NAME"] = "test-service"
        os.environ["QUERY_INTELLIGENCE_GCP_PROJECT_ID"] = "test-project"
        os.environ["QUERY_INTELLIGENCE_SPANNER_INSTANCE"] = "test-instance"
        os.environ["QUERY_INTELLIGENCE_SPANNER_DATABASE"] = "test-database"
        
        settings = get_settings()
        assert settings.service_name == "test-service"
        
        # Change environment variable
        os.environ["QUERY_INTELLIGENCE_SERVICE_NAME"] = "changed-service"
        
        # Should still return cached value
        settings2 = get_settings()
        assert settings2.service_name == "test-service"
        
        # Clean up
        del os.environ["QUERY_INTELLIGENCE_SERVICE_NAME"]
        del os.environ["QUERY_INTELLIGENCE_GCP_PROJECT_ID"]
        del os.environ["QUERY_INTELLIGENCE_SPANNER_INSTANCE"]
        del os.environ["QUERY_INTELLIGENCE_SPANNER_DATABASE"]
        get_settings.cache_clear()