"""
Unit tests for Pydantic models
"""

import pytest
from pydantic import ValidationError

from query_intelligence.models import (
    CodeReference,
    QueryContext,
    QueryIntent,
    QueryRequest,
    QueryResponse,
)


class TestQueryRequest:
    """Tests for QueryRequest model"""
    
    def test_valid_query_request(self):
        """Test creating a valid query request"""
        request = QueryRequest(
            query="How does the authentication system work?",
            repository_id="repo_abc123def456ghi7",
            include_code_snippets=True,
            max_results=5
        )
        
        assert request.query == "How does the authentication system work?"
        assert request.repository_id == "repo_abc123def456ghi7"
        assert request.include_code_snippets is True
        assert request.max_results == 5
    
    def test_query_validation(self):
        """Test query validation"""
        # Too short
        with pytest.raises(ValidationError) as exc:
            QueryRequest(
                query="Hi",
                repository_id="repo_abc123def456ghi7"
            )
        assert "at least 2 words" in str(exc.value)
        
        # Too long
        with pytest.raises(ValidationError) as exc:
            QueryRequest(
                query="x" * 1001,
                repository_id="repo_abc123def456ghi7"
            )
        assert "String should have at most 1000 characters" in str(exc.value)
    
    def test_repository_id_validation(self):
        """Test repository ID validation"""
        with pytest.raises(ValidationError) as exc:
            QueryRequest(
                query="How does authentication work?",
                repository_id="invalid-id"
            )
        assert "String should match pattern" in str(exc.value)
    
    def test_query_whitespace_stripping(self):
        """Test that query whitespace is handled properly"""
        request = QueryRequest(
            query="  How  does   authentication    work?  ",
            repository_id="repo_abc123def456ghi7"
        )
        assert request.query == "How does authentication work?"


class TestCodeReference:
    """Tests for CodeReference model"""
    
    def test_valid_code_reference(self):
        """Test creating a valid code reference"""
        ref = CodeReference(
            file_path="src/auth/middleware.py",
            start_line=45,
            end_line=72,
            snippet="def authenticate(request):\n    ...",
            relevance_score=0.92,
            symbol="authenticate",
            language="python"
        )
        
        assert ref.file_path == "src/auth/middleware.py"
        assert ref.start_line == 45
        assert ref.end_line == 72
        assert ref.relevance_score == 0.92
    
    def test_line_range_validation(self):
        """Test that end_line >= start_line"""
        with pytest.raises(ValidationError) as exc:
            CodeReference(
                file_path="src/auth/middleware.py",
                start_line=50,
                end_line=40,
                relevance_score=0.9
            )
        assert "end_line must be greater than or equal to start_line" in str(exc.value)
    
    def test_relevance_score_bounds(self):
        """Test relevance score bounds"""
        with pytest.raises(ValidationError):
            CodeReference(
                file_path="src/auth/middleware.py",
                start_line=1,
                end_line=10,
                relevance_score=1.5
            )


class TestQueryResponse:
    """Tests for QueryResponse model"""
    
    def test_valid_query_response(self):
        """Test creating a valid query response"""
        response = QueryResponse(
            query_id="query_abc123def456ghi7",
            answer="The authentication system uses JWT tokens...",
            confidence=0.89,
            intent=QueryIntent.EXPLAIN,
            references=[],
            follow_up_questions=["How are tokens validated?"],
            execution_time_ms=234.5,
            cached=False
        )
        
        assert response.query_id == "query_abc123def456ghi7"
        assert response.confidence == 0.89
        assert response.intent == QueryIntent.EXPLAIN
        assert len(response.follow_up_questions) == 1
    
    def test_query_id_validation(self):
        """Test query ID validation"""
        with pytest.raises(ValidationError):
            QueryResponse(
                query_id="invalid-id",
                answer="Test answer",
                confidence=0.9,
                intent=QueryIntent.EXPLAIN,
                references=[],
                execution_time_ms=100.0
            )


class TestQueryContext:
    """Tests for QueryContext model"""
    
    def test_valid_query_context(self):
        """Test creating a valid query context"""
        context = QueryContext(
            repository_id="repo_abc123def456ghi7",
            user_id="user_xyz789abc123def",
            session_id="session_123456789abcdef",
            history=[
                {
                    "query": "Previous query",
                    "response": "Previous response",
                    "timestamp": "2025-07-07T10:00:00Z"
                }
            ]
        )
        
        assert context.repository_id == "repo_abc123def456ghi7"
        assert context.user_id == "user_xyz789abc123def"
        assert len(context.history) == 1
    
    def test_history_validation(self):
        """Test history item validation"""
        with pytest.raises(ValidationError) as exc:
            QueryContext(
                repository_id="repo_abc123def456ghi7",
                history=[{"query": "Only query field"}]
            )
        assert "query, response, and timestamp" in str(exc.value)