#!/usr/bin/env python3
"""
Agent Coordinator for CCL Platform
Manages communication and coordination between multiple Claude Code agents
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import aiofiles
from asyncio import Queue


logger = logging.getLogger(__name__)


@dataclass
class AgentMessage:
    """Message passed between agents"""
    from_agent: str
    to_agent: Optional[str]  # None for broadcast
    message_type: str
    content: Dict[str, Any]
    timestamp: datetime
    correlation_id: Optional[str] = None


@dataclass
class SharedKnowledge:
    """Shared knowledge structure"""
    api_contracts: Dict[str, Any]
    database_schemas: Dict[str, Any]
    architectural_decisions: List[Dict[str, Any]]
    patterns: List[Dict[str, Any]]
    dependencies: Dict[str, List[str]]


class AgentCoordinator:
    """Coordinates communication between multiple agents"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.knowledge_bank_path = project_root / ".claude/memory/knowledge-bank.json"
        self.message_queue: Queue[AgentMessage] = Queue()
        self.agent_queues: Dict[str, Queue] = {}
        self.shared_knowledge = SharedKnowledge(
            api_contracts={},
            database_schemas={},
            architectural_decisions=[],
            patterns=[],
            dependencies={}
        )
        self.conflict_resolver = ConflictResolver()
        
    async def initialize(self):
        """Initialize the coordinator"""
        await self.load_knowledge_bank()
        logger.info("Agent coordinator initialized")
        
    async def register_agent(self, agent_id: str) -> Queue:
        """Register a new agent and return its message queue"""
        if agent_id not in self.agent_queues:
            self.agent_queues[agent_id] = Queue()
            logger.info(f"Registered agent: {agent_id}")
        return self.agent_queues[agent_id]
        
    async def unregister_agent(self, agent_id: str):
        """Unregister an agent"""
        if agent_id in self.agent_queues:
            del self.agent_queues[agent_id]
            logger.info(f"Unregistered agent: {agent_id}")
            
    async def broadcast_message(self, message: AgentMessage):
        """Broadcast a message to all agents"""
        for agent_id, queue in self.agent_queues.items():
            if agent_id != message.from_agent:
                await queue.put(message)
        logger.debug(f"Broadcasted message from {message.from_agent}")
        
    async def send_message(self, message: AgentMessage):
        """Send a message to a specific agent or broadcast"""
        if message.to_agent:
            if message.to_agent in self.agent_queues:
                await self.agent_queues[message.to_agent].put(message)
                logger.debug(f"Sent message from {message.from_agent} to {message.to_agent}")
        else:
            await self.broadcast_message(message)
            
    async def process_messages(self):
        """Main message processing loop"""
        while True:
            try:
                message = await self.message_queue.get()
                await self.handle_message(message)
            except Exception as e:
                logger.error(f"Error processing message: {e}")
                
    async def handle_message(self, message: AgentMessage):
        """Handle different types of messages"""
        if message.message_type == "api_contract":
            await self.handle_api_contract(message)
        elif message.message_type == "database_schema":
            await self.handle_database_schema(message)
        elif message.message_type == "architectural_decision":
            await self.handle_architectural_decision(message)
        elif message.message_type == "pattern_discovered":
            await self.handle_pattern_discovered(message)
        elif message.message_type == "dependency_update":
            await self.handle_dependency_update(message)
        elif message.message_type == "conflict":
            await self.handle_conflict(message)
        elif message.message_type == "sync_request":
            await self.handle_sync_request(message)
        else:
            logger.warning(f"Unknown message type: {message.message_type}")
            
    async def handle_api_contract(self, message: AgentMessage):
        """Handle API contract updates"""
        contract = message.content
        service_name = contract.get("service")
        
        if service_name in self.shared_knowledge.api_contracts:
            # Check for conflicts
            existing = self.shared_knowledge.api_contracts[service_name]
            if existing != contract:
                conflict = await self.conflict_resolver.resolve_api_conflict(
                    existing, contract, message.from_agent
                )
                if conflict.resolution == "merge":
                    self.shared_knowledge.api_contracts[service_name] = conflict.merged_data
                elif conflict.resolution == "keep_new":
                    self.shared_knowledge.api_contracts[service_name] = contract
                # else keep existing
        else:
            self.shared_knowledge.api_contracts[service_name] = contract
            
        await self.save_knowledge_bank()
        await self.notify_api_update(service_name)
        
    async def handle_database_schema(self, message: AgentMessage):
        """Handle database schema updates"""
        schema = message.content
        table_name = schema.get("table")
        
        self.shared_knowledge.database_schemas[table_name] = schema
        await self.save_knowledge_bank()
        await self.notify_schema_update(table_name)
        
    async def handle_architectural_decision(self, message: AgentMessage):
        """Handle architectural decision records"""
        decision = message.content
        decision["author"] = message.from_agent
        decision["timestamp"] = message.timestamp.isoformat()
        
        self.shared_knowledge.architectural_decisions.append(decision)
        await self.save_knowledge_bank()
        await self.notify_architectural_decision(decision)
        
    async def handle_pattern_discovered(self, message: AgentMessage):
        """Handle newly discovered patterns"""
        pattern = message.content
        pattern["discovered_by"] = message.from_agent
        pattern["timestamp"] = message.timestamp.isoformat()
        
        # Check if pattern already exists
        existing = next(
            (p for p in self.shared_knowledge.patterns 
             if p.get("name") == pattern.get("name")),
            None
        )
        
        if not existing:
            self.shared_knowledge.patterns.append(pattern)
            await self.save_knowledge_bank()
            await self.notify_pattern_discovered(pattern)
            
    async def handle_dependency_update(self, message: AgentMessage):
        """Handle dependency updates"""
        service = message.content.get("service")
        dependencies = message.content.get("dependencies", [])
        
        self.shared_knowledge.dependencies[service] = dependencies
        await self.save_knowledge_bank()
        
    async def handle_conflict(self, message: AgentMessage):
        """Handle conflict reports"""
        conflict_type = message.content.get("type")
        conflict_data = message.content.get("data")
        
        logger.warning(f"Conflict reported by {message.from_agent}: {conflict_type}")
        
        # Attempt automatic resolution
        resolution = await self.conflict_resolver.resolve(conflict_type, conflict_data)
        
        if resolution.automatic:
            # Apply resolution
            await self.apply_resolution(resolution)
        else:
            # Escalate to human
            await self.escalate_conflict(message, resolution)
            
    async def handle_sync_request(self, message: AgentMessage):
        """Handle synchronization requests"""
        sync_type = message.content.get("type", "full")
        
        if sync_type == "full":
            # Send full knowledge bank
            response = AgentMessage(
                from_agent="coordinator",
                to_agent=message.from_agent,
                message_type="sync_response",
                content={
                    "type": "full",
                    "data": self.shared_knowledge.__dict__
                },
                timestamp=datetime.now(),
                correlation_id=message.correlation_id
            )
            await self.send_message(response)
        elif sync_type == "api_contracts":
            response = AgentMessage(
                from_agent="coordinator",
                to_agent=message.from_agent,
                message_type="sync_response",
                content={
                    "type": "api_contracts",
                    "data": self.shared_knowledge.api_contracts
                },
                timestamp=datetime.now(),
                correlation_id=message.correlation_id
            )
            await self.send_message(response)
            
    async def notify_api_update(self, service_name: str):
        """Notify all agents of API contract update"""
        notification = AgentMessage(
            from_agent="coordinator",
            to_agent=None,  # Broadcast
            message_type="notification",
            content={
                "type": "api_updated",
                "service": service_name,
                "contract": self.shared_knowledge.api_contracts[service_name]
            },
            timestamp=datetime.now()
        )
        await self.broadcast_message(notification)
        
    async def notify_schema_update(self, table_name: str):
        """Notify all agents of schema update"""
        notification = AgentMessage(
            from_agent="coordinator",
            to_agent=None,
            message_type="notification",
            content={
                "type": "schema_updated",
                "table": table_name,
                "schema": self.shared_knowledge.database_schemas[table_name]
            },
            timestamp=datetime.now()
        )
        await self.broadcast_message(notification)
        
    async def notify_architectural_decision(self, decision: Dict):
        """Notify all agents of new architectural decision"""
        notification = AgentMessage(
            from_agent="coordinator",
            to_agent=None,
            message_type="notification",
            content={
                "type": "architectural_decision",
                "decision": decision
            },
            timestamp=datetime.now()
        )
        await self.broadcast_message(notification)
        
    async def notify_pattern_discovered(self, pattern: Dict):
        """Notify all agents of newly discovered pattern"""
        notification = AgentMessage(
            from_agent="coordinator",
            to_agent=None,
            message_type="notification",
            content={
                "type": "pattern_discovered",
                "pattern": pattern
            },
            timestamp=datetime.now()
        )
        await self.broadcast_message(notification)
        
    async def load_knowledge_bank(self):
        """Load shared knowledge from file"""
        if self.knowledge_bank_path.exists():
            async with aiofiles.open(self.knowledge_bank_path, 'r') as f:
                data = json.loads(await f.read())
                shared = data.get("shared", {})
                self.shared_knowledge = SharedKnowledge(
                    api_contracts=shared.get("api_contracts", {}),
                    database_schemas=shared.get("database_schemas", {}),
                    architectural_decisions=shared.get("architectural_decisions", []),
                    patterns=shared.get("patterns", []),
                    dependencies=shared.get("dependencies", {})
                )
                
    async def save_knowledge_bank(self):
        """Save shared knowledge to file"""
        async with aiofiles.open(self.knowledge_bank_path, 'r') as f:
            data = json.loads(await f.read())
            
        data["shared"] = {
            "api_contracts": self.shared_knowledge.api_contracts,
            "database_schemas": self.shared_knowledge.database_schemas,
            "architectural_decisions": self.shared_knowledge.architectural_decisions,
            "patterns": self.shared_knowledge.patterns,
            "dependencies": self.shared_knowledge.dependencies,
            "last_updated": datetime.now().isoformat()
        }
        
        async with aiofiles.open(self.knowledge_bank_path, 'w') as f:
            await f.write(json.dumps(data, indent=2))
            
    async def get_agent_context(self, agent_id: str, task_type: str) -> Dict:
        """Get relevant context for an agent based on task type"""
        context = {
            "timestamp": datetime.now().isoformat(),
            "agent_id": agent_id,
            "task_type": task_type
        }
        
        # Add relevant shared knowledge based on task type
        if task_type in ["backend-dev", "api"]:
            context["api_contracts"] = self.shared_knowledge.api_contracts
            context["dependencies"] = self.shared_knowledge.dependencies
            
        if task_type in ["database-eng", "backend-dev"]:
            context["database_schemas"] = self.shared_knowledge.database_schemas
            
        if task_type == "architect":
            context["architectural_decisions"] = self.shared_knowledge.architectural_decisions
            context["api_contracts"] = self.shared_knowledge.api_contracts
            context["database_schemas"] = self.shared_knowledge.database_schemas
            
        if task_type == "tester":
            context["api_contracts"] = self.shared_knowledge.api_contracts
            context["patterns"] = self.shared_knowledge.patterns
            
        return context


class ConflictResolver:
    """Resolves conflicts between agent updates"""
    
    @dataclass
    class Resolution:
        resolution: str  # "keep_existing", "keep_new", "merge", "escalate"
        automatic: bool
        merged_data: Optional[Any] = None
        reason: Optional[str] = None
        
    async def resolve(self, conflict_type: str, conflict_data: Dict) -> Resolution:
        """Resolve a conflict based on type"""
        if conflict_type == "api_contract":
            return await self.resolve_api_conflict(
                conflict_data["existing"],
                conflict_data["new"],
                conflict_data["agent"]
            )
        elif conflict_type == "database_schema":
            return await self.resolve_schema_conflict(
                conflict_data["existing"],
                conflict_data["new"],
                conflict_data["agent"]
            )
        else:
            return self.Resolution(
                resolution="escalate",
                automatic=False,
                reason=f"Unknown conflict type: {conflict_type}"
            )
            
    async def resolve_api_conflict(
        self, 
        existing: Dict, 
        new: Dict, 
        agent: str
    ) -> Resolution:
        """Resolve API contract conflicts"""
        # Simple resolution logic - can be made more sophisticated
        
        # If versions are specified, use the newer version
        if "version" in existing and "version" in new:
            if new["version"] > existing["version"]:
                return self.Resolution(
                    resolution="keep_new",
                    automatic=True,
                    reason="Newer version"
                )
            else:
                return self.Resolution(
                    resolution="keep_existing",
                    automatic=True,
                    reason="Existing version is newer"
                )
                
        # If architect made the change, prioritize it
        if agent == "agent-architect-1":
            return self.Resolution(
                resolution="keep_new",
                automatic=True,
                reason="Architect has priority"
            )
            
        # Otherwise, escalate for manual resolution
        return self.Resolution(
            resolution="escalate",
            automatic=False,
            reason="Unable to automatically resolve API contract conflict"
        )
        
    async def resolve_schema_conflict(
        self, 
        existing: Dict, 
        new: Dict, 
        agent: str
    ) -> Resolution:
        """Resolve database schema conflicts"""
        # Database engineer has priority for schema changes
        if "database-eng" in agent:
            return self.Resolution(
                resolution="keep_new",
                automatic=True,
                reason="Database engineer has priority for schema changes"
            )
            
        return self.Resolution(
            resolution="escalate",
            automatic=False,
            reason="Schema changes require database engineer approval"
        )


async def main():
    """Example usage"""
    project_root = Path(__file__).parent.parent
    coordinator = AgentCoordinator(project_root)
    
    await coordinator.initialize()
    
    # Example: Register agents
    agent1_queue = await coordinator.register_agent("agent-backend-1")
    agent2_queue = await coordinator.register_agent("agent-frontend-1")
    
    # Example: Send API contract update
    api_message = AgentMessage(
        from_agent="agent-backend-1",
        to_agent=None,
        message_type="api_contract",
        content={
            "service": "auth",
            "version": "1.0.0",
            "endpoints": [
                {
                    "path": "/auth/login",
                    "method": "POST",
                    "request": {"email": "string", "password": "string"},
                    "response": {"token": "string", "user": "object"}
                }
            ]
        },
        timestamp=datetime.now()
    )
    
    await coordinator.send_message(api_message)
    
    # Start message processing
    await coordinator.process_messages()


if __name__ == "__main__":
    asyncio.run(main())