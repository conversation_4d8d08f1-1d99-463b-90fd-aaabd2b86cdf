#!/usr/bin/env python3
"""
Task Decomposer for CCL Platform
Breaks down complex projects into manageable tasks using AI assistance
"""

import json
import yaml
from pathlib import Path
from typing import List, Dict, Optional, Set
from dataclasses import dataclass
from enum import Enum
import networkx as nx
import matplotlib.pyplot as plt


class TaskComplexity(Enum):
    SIMPLE = 1
    MEDIUM = 2
    COMPLEX = 3
    VERY_COMPLEX = 4


@dataclass
class TaskNode:
    """Represents a task in the dependency graph"""
    id: str
    title: str
    description: str
    persona: str
    complexity: TaskComplexity
    estimated_hours: float
    dependencies: List[str]
    sparc_phase: str
    deliverables: List[str]
    acceptance_criteria: List[str]


class TaskDecomposer:
    """Decomposes project specifications into executable tasks"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.tasks: Dict[str, TaskNode] = {}
        self.dependency_graph = nx.DiGraph()
        
    def load_specifications(self, spec_file: Path) -> Dict:
        """Load project specifications from markdown file"""
        # In production, this would parse the markdown and extract requirements
        # For now, we'll use a predefined structure
        return {
            "services": [
                {
                    "name": "Analysis Engine",
                    "type": "backend",
                    "language": "rust",
                    "complexity": "high",
                    "features": [
                        "Multi-language parsing",
                        "AST generation",
                        "Pattern detection",
                        "Metrics calculation"
                    ]
                },
                {
                    "name": "Query Intelligence",
                    "type": "backend",
                    "language": "python",
                    "complexity": "high",
                    "features": [
                        "Natural language processing",
                        "Gemini integration",
                        "Context management",
                        "Response generation"
                    ]
                },
                {
                    "name": "Pattern Mining",
                    "type": "backend",
                    "language": "python",
                    "complexity": "medium",
                    "features": [
                        "ML pattern detection",
                        "Clustering",
                        "Quality scoring"
                    ]
                },
                {
                    "name": "Marketplace",
                    "type": "backend",
                    "language": "go",
                    "complexity": "medium",
                    "features": [
                        "Pattern management",
                        "Payment processing",
                        "Search functionality"
                    ]
                },
                {
                    "name": "Admin Dashboard",
                    "type": "frontend",
                    "language": "typescript",
                    "complexity": "medium",
                    "features": [
                        "User management",
                        "Analytics",
                        "System monitoring"
                    ]
                }
            ],
            "infrastructure": {
                "database": "spanner",
                "cache": "redis",
                "queue": "pubsub",
                "storage": "gcs"
            }
        }
        
    def decompose_project(self, specifications: Dict) -> List[TaskNode]:
        """Decompose project into tasks based on specifications"""
        tasks = []
        
        # Phase 1: Architecture Tasks
        tasks.extend(self._create_architecture_tasks(specifications))
        
        # Phase 2: Infrastructure Tasks
        tasks.extend(self._create_infrastructure_tasks(specifications))
        
        # Phase 3: Service Implementation Tasks
        tasks.extend(self._create_service_tasks(specifications))
        
        # Phase 4: Integration Tasks
        tasks.extend(self._create_integration_tasks(specifications))
        
        # Phase 5: Testing Tasks
        tasks.extend(self._create_testing_tasks(specifications))
        
        # Build dependency graph
        self._build_dependency_graph(tasks)
        
        return tasks
        
    def _create_architecture_tasks(self, spec: Dict) -> List[TaskNode]:
        """Create architecture and design tasks"""
        return [
            TaskNode(
                id="arch-001",
                title="Design Overall System Architecture",
                description="Create comprehensive system architecture including all services, data flow, and integration points",
                persona="architect",
                complexity=TaskComplexity.COMPLEX,
                estimated_hours=8,
                dependencies=[],
                sparc_phase="architecture",
                deliverables=[
                    "System architecture diagram",
                    "Component interaction diagram",
                    "Data flow diagram",
                    "Architecture decision records"
                ],
                acceptance_criteria=[
                    "All services defined with clear boundaries",
                    "Scalability considerations documented",
                    "Security architecture defined",
                    "Performance targets specified"
                ]
            ),
            TaskNode(
                id="arch-002",
                title="Design API Contracts",
                description="Define all API endpoints, request/response formats, and contracts between services",
                persona="architect",
                complexity=TaskComplexity.MEDIUM,
                estimated_hours=6,
                dependencies=["arch-001"],
                sparc_phase="architecture",
                deliverables=[
                    "OpenAPI specifications",
                    "GraphQL schema",
                    "Event schemas",
                    "API versioning strategy"
                ],
                acceptance_criteria=[
                    "All endpoints documented",
                    "Request/response examples provided",
                    "Error codes defined",
                    "Rate limiting specified"
                ]
            ),
            TaskNode(
                id="arch-003",
                title="Design Database Schemas",
                description="Create database schemas for Spanner, define indexes, and plan data distribution",
                persona="database-eng",
                complexity=TaskComplexity.MEDIUM,
                estimated_hours=6,
                dependencies=["arch-001"],
                sparc_phase="architecture",
                deliverables=[
                    "Spanner schema DDL",
                    "Index strategy",
                    "Sharding plan",
                    "Migration scripts"
                ],
                acceptance_criteria=[
                    "All entities defined",
                    "Relationships established",
                    "Performance indexes created",
                    "Scalability planned"
                ]
            )
        ]
        
    def _create_infrastructure_tasks(self, spec: Dict) -> List[TaskNode]:
        """Create infrastructure setup tasks"""
        return [
            TaskNode(
                id="infra-001",
                title="Setup GCP Infrastructure",
                description="Create all GCP resources using Terraform including VPC, service accounts, and base services",
                persona="devops",
                complexity=TaskComplexity.MEDIUM,
                estimated_hours=6,
                dependencies=["arch-001"],
                sparc_phase="implementation",
                deliverables=[
                    "Terraform configurations",
                    "VPC setup",
                    "Service accounts",
                    "IAM policies"
                ],
                acceptance_criteria=[
                    "All resources created",
                    "Security best practices followed",
                    "Cost optimization implemented",
                    "Multi-region support"
                ]
            ),
            TaskNode(
                id="infra-002",
                title="Setup CI/CD Pipelines",
                description="Create GitHub Actions workflows for all services including build, test, and deployment",
                persona="devops",
                complexity=TaskComplexity.MEDIUM,
                estimated_hours=8,
                dependencies=["infra-001"],
                sparc_phase="implementation",
                deliverables=[
                    "GitHub Actions workflows",
                    "Build configurations",
                    "Deployment scripts",
                    "Environment configurations"
                ],
                acceptance_criteria=[
                    "All services have CI/CD",
                    "Automated testing integrated",
                    "Security scanning included",
                    "Blue-green deployment"
                ]
            )
        ]
        
    def _create_service_tasks(self, spec: Dict) -> List[TaskNode]:
        """Create service implementation tasks"""
        tasks = []
        
        for service in spec["services"]:
            service_id = service["name"].lower().replace(" ", "-")
            
            # API implementation task
            tasks.append(TaskNode(
                id=f"{service_id}-impl",
                title=f"Implement {service['name']} Service",
                description=f"Build the {service['name']} service with all core features",
                persona=self._get_persona_for_service(service),
                complexity=self._get_complexity(service["complexity"]),
                estimated_hours=self._estimate_hours(service),
                dependencies=["arch-002", "infra-001"],
                sparc_phase="refinement",
                deliverables=[
                    f"{service['name']} service code",
                    "Unit tests",
                    "Service documentation",
                    "Dockerfile"
                ],
                acceptance_criteria=[
                    "All features implemented",
                    "Tests passing with >80% coverage",
                    "API endpoints working",
                    "Performance benchmarks met"
                ]
            ))
            
            # Testing task
            tasks.append(TaskNode(
                id=f"{service_id}-test",
                title=f"Create Tests for {service['name']}",
                description=f"Comprehensive test suite including unit, integration, and e2e tests",
                persona="tester",
                complexity=TaskComplexity.MEDIUM,
                estimated_hours=4,
                dependencies=[f"{service_id}-impl"],
                sparc_phase="completion",
                deliverables=[
                    "Unit test suite",
                    "Integration tests",
                    "E2E test scenarios",
                    "Test documentation"
                ],
                acceptance_criteria=[
                    "80%+ code coverage",
                    "All endpoints tested",
                    "Error cases covered",
                    "Performance tests included"
                ]
            ))
            
        return tasks
        
    def _create_integration_tasks(self, spec: Dict) -> List[TaskNode]:
        """Create integration and deployment tasks"""
        return [
            TaskNode(
                id="int-001",
                title="Service Integration Testing",
                description="Test all services working together with full data flow",
                persona="tester",
                complexity=TaskComplexity.COMPLEX,
                estimated_hours=8,
                dependencies=[
                    "analysis-engine-impl",
                    "query-intelligence-impl",
                    "pattern-mining-impl"
                ],
                sparc_phase="completion",
                deliverables=[
                    "Integration test suite",
                    "Performance benchmarks",
                    "Load test results",
                    "Integration documentation"
                ],
                acceptance_criteria=[
                    "All service interactions tested",
                    "Performance SLAs met",
                    "Error handling verified",
                    "Data consistency validated"
                ]
            )
        ]
        
    def _create_testing_tasks(self, spec: Dict) -> List[TaskNode]:
        """Create comprehensive testing tasks"""
        return [
            TaskNode(
                id="test-001",
                title="Security Testing",
                description="Perform comprehensive security testing including OWASP checks",
                persona="tester",
                complexity=TaskComplexity.MEDIUM,
                estimated_hours=6,
                dependencies=["int-001"],
                sparc_phase="completion",
                deliverables=[
                    "Security test report",
                    "Vulnerability assessment",
                    "Penetration test results",
                    "Remediation plan"
                ],
                acceptance_criteria=[
                    "No critical vulnerabilities",
                    "OWASP top 10 covered",
                    "Authentication tested",
                    "Data encryption verified"
                ]
            )
        ]
        
    def _get_persona_for_service(self, service: Dict) -> str:
        """Determine the appropriate persona for a service"""
        if service["type"] == "frontend":
            return "frontend-dev"
        elif service["language"] == "rust":
            return "backend-dev"  # Rust specialist
        elif service["language"] == "python":
            return "backend-dev"  # Python specialist
        elif service["language"] == "go":
            return "backend-dev"  # Go specialist
        else:
            return "backend-dev"
            
    def _get_complexity(self, complexity_str: str) -> TaskComplexity:
        """Convert string complexity to enum"""
        mapping = {
            "simple": TaskComplexity.SIMPLE,
            "medium": TaskComplexity.MEDIUM,
            "high": TaskComplexity.COMPLEX,
            "very_high": TaskComplexity.VERY_COMPLEX
        }
        return mapping.get(complexity_str, TaskComplexity.MEDIUM)
        
    def _estimate_hours(self, service: Dict) -> float:
        """Estimate hours based on service complexity and features"""
        base_hours = {
            "simple": 8,
            "medium": 16,
            "high": 32,
            "very_high": 48
        }
        
        hours = base_hours.get(service["complexity"], 16)
        # Add time for each feature
        hours += len(service.get("features", [])) * 2
        
        return hours
        
    def _build_dependency_graph(self, tasks: List[TaskNode]):
        """Build a dependency graph from tasks"""
        for task in tasks:
            self.tasks[task.id] = task
            self.dependency_graph.add_node(task.id, task=task)
            
        for task in tasks:
            for dep in task.dependencies:
                if dep in self.tasks:
                    self.dependency_graph.add_edge(dep, task.id)
                    
    def visualize_dependencies(self, output_file: Path = None):
        """Create a visual representation of task dependencies"""
        plt.figure(figsize=(12, 8))
        
        # Calculate layout
        pos = nx.spring_layout(self.dependency_graph, k=2, iterations=50)
        
        # Color nodes by persona
        persona_colors = {
            "architect": "#FF6B6B",
            "backend-dev": "#4ECDC4",
            "frontend-dev": "#45B7D1",
            "database-eng": "#96CEB4",
            "devops": "#FECA57",
            "tester": "#DDA0DD"
        }
        
        node_colors = [
            persona_colors.get(self.tasks[node].persona, "#95A5A6")
            for node in self.dependency_graph.nodes()
        ]
        
        # Draw the graph
        nx.draw(
            self.dependency_graph,
            pos,
            node_color=node_colors,
            node_size=2000,
            with_labels=True,
            font_size=8,
            font_weight="bold",
            arrows=True,
            edge_color="#95A5A6",
            alpha=0.8
        )
        
        # Add title
        plt.title("Task Dependency Graph", fontsize=16, fontweight="bold")
        
        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches="tight")
        else:
            plt.show()
            
    def get_execution_order(self) -> List[List[str]]:
        """Get tasks in execution order (topological sort with parallel groups)"""
        # Find all tasks that can be executed in parallel
        levels = {}
        for node in nx.topological_sort(self.dependency_graph):
            # Find the maximum level of dependencies
            if self.dependency_graph.in_degree(node) == 0:
                levels[node] = 0
            else:
                max_level = max(
                    levels[pred] for pred in self.dependency_graph.predecessors(node)
                )
                levels[node] = max_level + 1
                
        # Group tasks by level
        execution_order = {}
        for node, level in levels.items():
            if level not in execution_order:
                execution_order[level] = []
            execution_order[level].append(node)
            
        return [execution_order[i] for i in sorted(execution_order.keys())]
        
    def export_tasks(self, output_file: Path):
        """Export tasks to JSON file"""
        tasks_data = []
        for task_id, task in self.tasks.items():
            tasks_data.append({
                "id": task.id,
                "title": task.title,
                "description": task.description,
                "persona": task.persona,
                "complexity": task.complexity.value,
                "estimated_hours": task.estimated_hours,
                "dependencies": task.dependencies,
                "sparc_phase": task.sparc_phase,
                "deliverables": task.deliverables,
                "acceptance_criteria": task.acceptance_criteria
            })
            
        with open(output_file, 'w') as f:
            json.dump(tasks_data, f, indent=2)
            
    def generate_gantt_chart(self, output_file: Path = None):
        """Generate a Gantt chart for the project timeline"""
        # This would use matplotlib or plotly to create a Gantt chart
        # For now, we'll create a simple timeline representation
        execution_order = self.get_execution_order()
        
        print("\n📅 Project Timeline (Parallel Execution)")
        print("=" * 60)
        
        total_hours = 0
        for level, tasks in enumerate(execution_order):
            max_hours = max(
                self.tasks[task_id].estimated_hours 
                for task_id in tasks
            )
            total_hours += max_hours
            
            print(f"\n🔹 Phase {level + 1} ({max_hours} hours)")
            for task_id in tasks:
                task = self.tasks[task_id]
                print(f"   - [{task.persona}] {task.title} ({task.estimated_hours}h)")
                
        print(f"\n📊 Total Duration: {total_hours} hours ({total_hours/8:.1f} days)")
        print(f"📊 Total Effort: {sum(t.estimated_hours for t in self.tasks.values())} person-hours")


def main():
    """Example usage of the task decomposer"""
    project_root = Path(__file__).parent.parent
    decomposer = TaskDecomposer(project_root)
    
    # Load specifications
    spec_file = project_root / "sparc/specifications/ccl-platform.md"
    specifications = decomposer.load_specifications(spec_file)
    
    # Decompose into tasks
    tasks = decomposer.decompose_project(specifications)
    
    # Export tasks
    output_file = project_root / "orchestration/generated_tasks.json"
    decomposer.export_tasks(output_file)
    
    # Visualize dependencies
    graph_file = project_root / "orchestration/task_dependencies.png"
    decomposer.visualize_dependencies(graph_file)
    
    # Generate timeline
    decomposer.generate_gantt_chart()
    
    print(f"\n✅ Generated {len(tasks)} tasks")
    print(f"📁 Tasks exported to: {output_file}")
    print(f"📊 Dependency graph saved to: {graph_file}")


if __name__ == "__main__":
    main()