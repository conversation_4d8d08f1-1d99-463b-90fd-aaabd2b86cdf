{"name": "CCL Platform Development", "description": "AI-powered code intelligence platform orchestration", "version": "1.0.0", "settings": {"autoApprove": false, "defaultModel": "claude-3-opus-20240229", "enableMCP": true, "enableLogging": true, "logLevel": "info", "workspaceRoot": "/Users/<USER>/Documents/GitHub/episteme"}, "models": [{"provider": "anthropic", "model": "claude-3-opus-20240229", "apiKey": "${ANTHROPIC_API_KEY}"}, {"provider": "openai", "model": "gpt-4-turbo-preview", "apiKey": "${OPENAI_API_KEY}"}, {"provider": "google", "model": "gemini-2.0-pro", "apiKey": "${GOOGLE_API_KEY}"}], "mcpServers": {"filesystem": {"enabled": true, "config": {"directories": ["/Users/<USER>/Documents/GitHub/episteme"]}}, "git": {"enabled": true, "config": {"repositories": ["."]}}, "memory": {"enabled": true, "config": {"storePath": ".claude/memory/cline-memory.json"}}}, "workflows": {"orchestrate": {"name": "Launch Orchestration", "description": "Start the multi-agent development system", "steps": [{"type": "terminal", "command": "python orchestration/launch_agents.py"}, {"type": "browser", "url": "file:///Users/<USER>/Documents/GitHub/episteme/monitoring/dashboard.html"}]}, "validate": {"name": "Validate Code", "description": "Run comprehensive validation", "steps": [{"type": "terminal", "command": "make lint"}, {"type": "terminal", "command": "make test"}, {"type": "terminal", "command": "make security-scan"}]}, "sync-agents": {"name": "Sync Agent Knowledge", "description": "Synchronize knowledge between agents", "steps": [{"type": "file", "action": "read", "path": ".claude/memory/knowledge-bank.json"}, {"type": "prompt", "message": "Review agent synchronization status"}]}}, "customCommands": {"sparc": {"description": "Execute SPARC methodology phase", "handler": "sparc-handler.js"}, "monitor": {"description": "Open monitoring dashboard", "handler": "monitor-handler.js"}}, "approvalRules": {"requireApproval": ["file:delete", "terminal:rm", "terminal:git push", "file:write:*.env", "file:write:*.key"], "autoApprove": ["file:read", "terminal:ls", "terminal:git status", "terminal:make test"]}, "contextRules": {"alwaysInclude": ["CLAUDE.md", "ORCHESTRATION.md", ".claude/memory/knowledge-bank.json"], "exclude": ["node_modules/**", "*.log", "*.tmp", ".git/**"], "maxFileSize": "1MB", "maxTotalContext": "200KB"}, "integrations": {"vscode": {"autoSave": true, "formatOnSave": true, "showInlineHints": true}, "git": {"autoCommit": false, "commitMessageTemplate": "[{agent}] {task}: {description}", "branchNaming": "agent/{agent}/{task-id}"}}, "monitoring": {"trackMetrics": true, "metricsEndpoint": "http://localhost:9090/metrics", "dashboardUrl": "file:///Users/<USER>/Documents/GitHub/episteme/monitoring/dashboard.html", "alerting": {"enabled": true, "channels": ["console", "file"]}}, "performance": {"maxConcurrentAgents": 10, "taskTimeout": 3600, "memoryLimit": "4GB", "cpuLimit": "80%"}}