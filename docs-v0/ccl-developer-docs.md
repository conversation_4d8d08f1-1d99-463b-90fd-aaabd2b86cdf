# CCL Developer Documentation
## Build Intelligent Applications with Code Understanding

**Version:** 1.0  
**Last Updated:** January 2025  
**API Version:** v1

---

## Welcome to CCL

CCL (Codebase Context Layer) provides AI-powered architectural intelligence for any codebase. This documentation will help you integrate CCL into your development workflow and build applications that understand code.

### What You Can Build

- **AI Coding Assistants** with deep codebase understanding
- **Automated Documentation** that stays current
- **Code Review Tools** that enforce patterns
- **Architecture Visualizers** with real intelligence
- **Technical Debt Analyzers** with actionable insights
- **Custom Development Tools** powered by code intelligence

---

## Quick Start

### 1. Installation

#### CLI Installation
```bash
# macOS/Linux
curl -fsSL https://install.ccl.dev | bash

# Windows
iwr -useb https://install.ccl.dev | iex

# Homebrew
brew install ccl/tap/ccl

# npm/yarn
npm install -g @ccl/cli
yarn global add @ccl/cli
```

#### SDK Installation

**JavaScript/TypeScript**
```bash
npm install @ccl/sdk
# or
yarn add @ccl/sdk
```

**Python**
```bash
pip install ccl-sdk
```

**Go**
```bash
go get github.com/ccl-platform/ccl-go
```

**Java**
```xml
<dependency>
    <groupId>dev.ccl</groupId>
    <artifactId>ccl-sdk</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. Authentication

#### Get Your API Key
1. Sign up at [ccl.dev](https://ccl.dev)
2. Navigate to Settings → API Keys
3. Create a new API key with appropriate scopes

#### Configure Authentication

**Environment Variable (Recommended)**
```bash
export CCL_API_KEY="your-api-key-here"
```

**CLI Configuration**
```bash
ccl auth login
# Opens browser for OAuth flow
```

**SDK Configuration**
```javascript
import { CCL } from '@ccl/sdk';

const ccl = new CCL({
  apiKey: process.env.CCL_API_KEY,
  // Optional: specify region
  region: 'us-central1'
});
```

### 3. Your First Analysis

**CLI**
```bash
# Analyze current directory
ccl analyze .

# Analyze GitHub repository
ccl analyze https://github.com/facebook/react

# Analyze with specific options
ccl analyze . --languages javascript,typescript --incremental
```

**SDK**
```javascript
// Analyze a repository
const analysis = await ccl.analyze({
  repositoryUrl: 'https://github.com/facebook/react',
  branch: 'main',
  languages: ['javascript', 'typescript']
});

// Check analysis status
const status = await ccl.getAnalysis(analysis.analysisId);
console.log(`Status: ${status.status}`);
console.log(`Progress: ${status.progress}%`);

// Get results when complete
if (status.status === 'completed') {
  console.log(`Files analyzed: ${status.statistics.filesAnalyzed}`);
  console.log(`Patterns found: ${status.statistics.patternsDetected}`);
}
```

### 4. Query the Codebase

**CLI**
```bash
# Ask a question
ccl query "How does authentication work?"

# Query specific repository
ccl query "What design patterns are used?" --repo react

# Start interactive session
ccl chat
```

**SDK**
```javascript
// Simple query
const response = await ccl.query({
  query: "How does the authentication system work?",
  repositoryId: 'your-repo-id'
});

console.log(response.answer);
console.log(`Confidence: ${response.confidence}`);

// With conversation context
const conversation = await ccl.createConversation({
  repositoryId: 'your-repo-id'
});

const response1 = await conversation.ask("What database is used?");
const response2 = await conversation.ask("How does it handle migrations?");
// Context is maintained between questions
```

---

## Core Concepts

### Repositories

A repository represents a codebase that CCL analyzes. It can be:
- A Git repository (GitHub, GitLab, Bitbucket)
- A local directory
- A cloud storage location (Google Cloud Storage, S3)

```javascript
// Create repository from GitHub
const repo = await ccl.repositories.create({
  url: 'https://github.com/your/repo',
  name: 'My Project',
  branch: 'main'
});

// Create from local directory
const localRepo = await ccl.repositories.createFromLocal({
  path: '/path/to/project',
  name: 'Local Project'
});

// List your repositories
const repos = await ccl.repositories.list();
```

### Analysis

Analysis extracts intelligence from your codebase:
- **AST Parsing**: Complete code structure understanding
- **Dependency Mapping**: How components connect
- **Pattern Detection**: Architectural and design patterns
- **Metric Calculation**: Complexity, maintainability, quality

```javascript
// Trigger analysis with options
const analysis = await ccl.analyze({
  repositoryId: repo.id,
  options: {
    incremental: true,  // Only analyze changes
    deepPatternAnalysis: true,  // Enhanced pattern detection
    generateEmbeddings: true,  // For semantic search
    excludePaths: ['node_modules', 'dist', 'build']
  }
});

// Monitor progress
const unsubscribe = ccl.onAnalysisProgress(analysis.id, (progress) => {
  console.log(`${progress.phase}: ${progress.percentage}%`);
});

// Wait for completion
await ccl.waitForAnalysis(analysis.id);
```

### Patterns

Patterns represent recurring architectural and design elements:

```javascript
// Get detected patterns
const patterns = await ccl.patterns.list({
  repositoryId: repo.id,
  minConfidence: 0.8
});

// Example pattern structure
{
  id: 'pattern_123',
  type: 'repository',
  name: 'Repository Pattern',
  confidence: 0.95,
  occurrences: 12,
  files: [
    'src/repositories/UserRepository.js',
    'src/repositories/ProductRepository.js'
  ],
  template: 'class ${Entity}Repository { ... }',
  description: 'Data access abstraction pattern'
}

// Get pattern details
const pattern = await ccl.patterns.get('pattern_123');

// Generate code from pattern
const code = await ccl.patterns.generateCode({
  patternId: 'pattern_123',
  variables: {
    Entity: 'Order'
  }
});
```

### Conversations

Natural language interface for querying codebases:

```javascript
// Create conversation with context
const conversation = await ccl.conversations.create({
  repositoryId: repo.id,
  context: {
    focusArea: 'authentication',
    userRole: 'senior-developer'
  }
});

// Ask questions
const response = await conversation.ask(
  "What authentication methods are supported?"
);

// Access conversation history
const history = await conversation.getHistory();

// Get suggested follow-up questions
console.log(response.suggestedQuestions);
// ["How is session management handled?", 
//  "Where are credentials stored?",
//  "What about OAuth integration?"]
```

---

## Advanced Features

### Pattern Marketplace

Access community-created patterns:

```javascript
// Search marketplace
const marketplacePatterns = await ccl.marketplace.search({
  query: 'authentication',
  language: 'javascript',
  category: 'security'
});

// Purchase and install pattern
await ccl.marketplace.purchase('pattern_abc');
await ccl.patterns.install('pattern_abc', {
  repositoryId: repo.id
});

// Publish your pattern
const published = await ccl.marketplace.publish({
  name: 'Advanced Repository Pattern',
  description: 'Repository pattern with caching and transactions',
  template: '...',
  price: 9.99,
  category: 'data-access',
  examples: ['...']
});
```

### Real-time Collaboration

Work with your team in real-time:

```javascript
// Create collaborative session
const session = await ccl.sessions.create({
  repositoryId: repo.id,
  name: 'Architecture Review'
});

// Join session
await ccl.sessions.join(session.id);

// Listen for updates
ccl.sessions.onUpdate(session.id, (update) => {
  console.log(`${update.user} is viewing ${update.file}`);
});

// Share cursor position
await ccl.sessions.updateCursor({
  file: 'src/auth/login.js',
  line: 42,
  column: 15
});
```

### Code Simulation

Predict the impact of changes:

```javascript
// Simulate architectural change
const simulation = await ccl.simulate({
  repositoryId: repo.id,
  change: {
    type: 'refactor',
    description: 'Convert monolith to microservices',
    constraints: {
      maxServices: 5,
      preserveApi: true
    }
  }
});

console.log(`Estimated effort: ${simulation.estimatedDays} days`);
console.log(`Risk level: ${simulation.riskLevel}`);
console.log(`Affected files: ${simulation.affectedFiles.length}`);

// Get detailed migration plan
const plan = await simulation.getMigrationPlan();
```

### Custom Pattern Training

Train CCL to recognize your team's patterns:

```javascript
// Provide examples of your pattern
const training = await ccl.patterns.train({
  name: 'Custom Service Pattern',
  examples: [
    { file: 'src/services/UserService.js', isPositive: true },
    { file: 'src/services/ProductService.js', isPositive: true },
    { file: 'src/utils/helper.js', isPositive: false }
  ],
  description: 'Our team\'s service layer pattern'
});

// Monitor training progress
await ccl.patterns.waitForTraining(training.id);

// Use trained pattern
const detected = await ccl.patterns.detect({
  repositoryId: repo.id,
  patternId: training.patternId
});
```

---

## API Reference

### Base URL
```
https://api.ccl.dev/v1
```

### Authentication
Include your API key in the Authorization header:
```
Authorization: Bearer YOUR_API_KEY
```

### Rate Limits
- Free: 1,000 requests/hour
- Pro: 10,000 requests/hour
- Team: 100,000 requests/hour
- Enterprise: Unlimited

### Common Endpoints

#### Analyze Repository
```http
POST /analyze
Content-Type: application/json

{
  "repository_url": "https://github.com/facebook/react",
  "branch": "main",
  "languages": ["javascript", "typescript"],
  "incremental": false
}

Response:
{
  "analysis_id": "ana_123abc",
  "status": "pending",
  "estimated_time_seconds": 300
}
```

#### Query Codebase
```http
POST /query
Content-Type: application/json

{
  "query": "How does the routing system work?",
  "repository_id": "repo_456def",
  "conversation_id": "conv_789ghi",
  "stream": true
}

Response (Streaming):
data: {"chunk": "The routing system ", "done": false}
data: {"chunk": "uses React Router ", "done": false}
data: {"chunk": "for navigation.", "done": true, "sources": [...]}
```

#### List Patterns
```http
GET /patterns?repository_id=repo_456def&min_confidence=0.8

Response:
{
  "patterns": [
    {
      "id": "pat_123",
      "type": "repository",
      "name": "Repository Pattern",
      "confidence": 0.95,
      "occurrences": 12
    }
  ],
  "pagination": {
    "total": 45,
    "page": 1,
    "per_page": 20
  }
}
```

### Error Handling

CCL uses standard HTTP status codes:

```javascript
try {
  const result = await ccl.analyze({...});
} catch (error) {
  if (error.code === 'RATE_LIMITED') {
    console.log(`Rate limited. Retry after: ${error.retryAfter}`);
  } else if (error.code === 'INVALID_REPOSITORY') {
    console.log('Repository URL is not accessible');
  } else if (error.code === 'INSUFFICIENT_PERMISSIONS') {
    console.log('Upgrade your plan for this feature');
  }
}
```

Common error codes:
- `RATE_LIMITED`: Too many requests
- `INVALID_API_KEY`: Authentication failed
- `INVALID_REPOSITORY`: Repository not accessible
- `ANALYSIS_FAILED`: Analysis encountered an error
- `INSUFFICIENT_PERMISSIONS`: Feature requires upgrade
- `NOT_FOUND`: Resource doesn't exist

---

## SDK Examples

### JavaScript/TypeScript

#### Complete Example: Code Review Assistant

```typescript
import { CCL } from '@ccl/sdk';

class CodeReviewAssistant {
  private ccl: CCL;
  private repoId: string;

  constructor(apiKey: string, repoId: string) {
    this.ccl = new CCL({ apiKey });
    this.repoId = repoId;
  }

  async reviewPullRequest(prNumber: number) {
    // Get PR changes
    const changes = await this.getPRChanges(prNumber);
    
    // Analyze changes against patterns
    const violations = [];
    
    for (const file of changes.files) {
      const analysis = await this.ccl.patterns.validateCode({
        repositoryId: this.repoId,
        code: file.content,
        filePath: file.path
      });
      
      if (analysis.violations.length > 0) {
        violations.push({
          file: file.path,
          violations: analysis.violations
        });
      }
    }
    
    // Generate review comment
    if (violations.length > 0) {
      const comment = await this.generateReviewComment(violations);
      await this.postPRComment(prNumber, comment);
    }
    
    return violations;
  }
  
  private async generateReviewComment(violations: any[]) {
    const response = await this.ccl.query({
      query: `Generate a constructive code review comment for these pattern violations: ${JSON.stringify(violations)}`,
      repositoryId: this.repoId
    });
    
    return response.answer;
  }
}
```

#### Real-time Architecture Explorer

```typescript
import { CCL } from '@ccl/sdk';
import express from 'express';
import { Server } from 'socket.io';

const app = express();
const io = new Server(app);
const ccl = new CCL({ apiKey: process.env.CCL_API_KEY });

io.on('connection', (socket) => {
  socket.on('explore-component', async (data) => {
    const { repoId, componentPath } = data;
    
    // Get component analysis
    const analysis = await ccl.query({
      query: `Explain the ${componentPath} component and its dependencies`,
      repositoryId: repoId
    });
    
    // Get visual dependency graph
    const dependencies = await ccl.getDependencyGraph({
      repositoryId: repoId,
      entryPoint: componentPath,
      depth: 3
    });
    
    socket.emit('component-analysis', {
      explanation: analysis.answer,
      dependencies: dependencies,
      patterns: analysis.patterns
    });
  });
});
```

### Python

#### Pattern Enforcement in CI/CD

```python
from ccl import CCL
import sys
import json

class PatternEnforcer:
    def __init__(self, api_key: str, repo_id: str):
        self.ccl = CCL(api_key=api_key)
        self.repo_id = repo_id
    
    def check_patterns(self, changed_files: list) -> bool:
        """Check if changed files follow established patterns"""
        
        violations = []
        
        for file_path in changed_files:
            with open(file_path, 'r') as f:
                code = f.read()
            
            # Validate against patterns
            result = self.ccl.patterns.validate_code(
                repository_id=self.repo_id,
                code=code,
                file_path=file_path
            )
            
            if result['violations']:
                violations.append({
                    'file': file_path,
                    'violations': result['violations']
                })
        
        if violations:
            print("❌ Pattern violations found:")
            for v in violations:
                print(f"\n{v['file']}:")
                for violation in v['violations']:
                    print(f"  - {violation['message']}")
                    print(f"    Suggestion: {violation['suggestion']}")
            return False
        
        print("✅ All files follow established patterns!")
        return True
    
    def suggest_improvements(self, file_path: str) -> list:
        """Get AI-powered improvement suggestions"""
        
        with open(file_path, 'r') as f:
            code = f.read()
        
        response = self.ccl.query(
            query=f"Suggest improvements for this code based on the team's patterns",
            repository_id=self.repo_id,
            context={'code': code, 'file': file_path}
        )
        
        return response['suggestions']

# CI/CD Integration
if __name__ == "__main__":
    enforcer = PatternEnforcer(
        api_key=os.environ['CCL_API_KEY'],
        repo_id=os.environ['CCL_REPO_ID']
    )
    
    # Get changed files from git
    changed_files = sys.argv[1:]
    
    if not enforcer.check_patterns(changed_files):
        sys.exit(1)
```

#### Architecture Documentation Generator

```python
from ccl import CCL
from typing import Dict, List
import markdown
import os

class DocGenerator:
    def __init__(self, api_key: str):
        self.ccl = CCL(api_key=api_key)
    
    async def generate_architecture_docs(self, repo_id: str, output_dir: str):
        """Generate comprehensive architecture documentation"""
        
        # Get high-level overview
        overview = await self.ccl.query(
            query="Provide a high-level architecture overview",
            repository_id=repo_id
        )
        
        # Get component documentation
        components = await self.ccl.get_components(repository_id=repo_id)
        
        # Create documentation structure
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate main README
        with open(f"{output_dir}/README.md", "w") as f:
            f.write(f"# Architecture Documentation\n\n")
            f.write(f"{overview['answer']}\n\n")
            f.write(f"## Components\n\n")
            
            for component in components:
                f.write(f"- [{component['name']}](./{component['name']}.md)\n")
        
        # Generate component docs
        for component in components:
            await self._generate_component_doc(
                repo_id, 
                component, 
                output_dir
            )
    
    async def _generate_component_doc(
        self, 
        repo_id: str, 
        component: Dict, 
        output_dir: str
    ):
        """Generate documentation for a single component"""
        
        # Get detailed analysis
        analysis = await self.ccl.query(
            query=f"Explain the {component['name']} component in detail",
            repository_id=repo_id
        )
        
        # Get patterns used
        patterns = await self.ccl.patterns.list(
            repository_id=repo_id,
            component=component['id']
        )
        
        # Generate markdown
        content = f"""# {component['name']}

## Overview
{analysis['answer']}

## Patterns Used
"""
        for pattern in patterns:
            content += f"- **{pattern['name']}** (Confidence: {pattern['confidence']:.2f})\n"
            content += f"  - {pattern['description']}\n\n"
        
        # Add dependencies
        deps = await self.ccl.get_dependencies(
            repository_id=repo_id,
            component_id=component['id']
        )
        
        content += "\n## Dependencies\n"
        for dep in deps['internal']:
            content += f"- {dep['name']} - {dep['purpose']}\n"
        
        # Write file
        with open(f"{output_dir}/{component['name']}.md", "w") as f:
            f.write(content)
```

### Go

#### Pattern Library Manager

```go
package main

import (
    "fmt"
    "log"
    "github.com/ccl-platform/ccl-go"
)

type PatternManager struct {
    client *ccl.Client
}

func NewPatternManager(apiKey string) *PatternManager {
    client := ccl.NewClient(apiKey)
    return &PatternManager{client: client}
}

// DiscoverPatterns finds patterns in a repository
func (pm *PatternManager) DiscoverPatterns(repoID string) error {
    // Trigger analysis
    analysis, err := pm.client.Analyze(ccl.AnalyzeOptions{
        RepositoryID: repoID,
        DeepPatternAnalysis: true,
    })
    if err != nil {
        return fmt.Errorf("analysis failed: %w", err)
    }
    
    // Wait for completion
    if err := pm.client.WaitForAnalysis(analysis.ID); err != nil {
        return fmt.Errorf("analysis wait failed: %w", err)
    }
    
    // Get discovered patterns
    patterns, err := pm.client.Patterns.List(ccl.PatternListOptions{
        RepositoryID: repoID,
        MinConfidence: 0.8,
    })
    if err != nil {
        return fmt.Errorf("pattern list failed: %w", err)
    }
    
    fmt.Printf("Discovered %d patterns:\n", len(patterns))
    for _, pattern := range patterns {
        fmt.Printf("- %s (confidence: %.2f)\n", 
            pattern.Name, 
            pattern.Confidence
        )
    }
    
    return nil
}

// ExportPatterns exports patterns for sharing
func (pm *PatternManager) ExportPatterns(repoID string) (*ccl.PatternLibrary, error) {
    patterns, err := pm.client.Patterns.List(ccl.PatternListOptions{
        RepositoryID: repoID,
        MinConfidence: 0.9,
    })
    if err != nil {
        return nil, err
    }
    
    library := &ccl.PatternLibrary{
        Name: "Team Pattern Library",
        Version: "1.0.0",
        Patterns: make([]ccl.PatternDefinition, 0),
    }
    
    for _, pattern := range patterns {
        definition := ccl.PatternDefinition{
            ID: pattern.ID,
            Name: pattern.Name,
            Description: pattern.Description,
            Template: pattern.Template,
            Examples: pattern.Examples,
            Category: pattern.Type,
        }
        library.Patterns = append(library.Patterns, definition)
    }
    
    return library, nil
}

// ValidateAgainstPatterns checks code against patterns
func (pm *PatternManager) ValidateAgainstPatterns(
    repoID string, 
    code string, 
    filePath string,
) (*ccl.ValidationResult, error) {
    return pm.client.Patterns.ValidateCode(ccl.ValidateOptions{
        RepositoryID: repoID,
        Code: code,
        FilePath: filePath,
    })
}
```

---

## Best Practices

### 1. Efficient Analysis

```javascript
// Use incremental analysis for large codebases
const analysis = await ccl.analyze({
  repositoryId: repo.id,
  options: {
    incremental: true,
    // Only analyze specific paths if needed
    includePaths: ['src', 'lib'],
    excludePaths: ['test', 'node_modules', 'dist']
  }
});

// Cache analysis results
const CACHE_DURATION = 3600; // 1 hour
const cacheKey = `analysis:${repo.id}`;
let analysis = await cache.get(cacheKey);

if (!analysis) {
  analysis = await ccl.getLatestAnalysis(repo.id);
  await cache.set(cacheKey, analysis, CACHE_DURATION);
}
```

### 2. Optimize Queries

```javascript
// Provide context for better answers
const response = await ccl.query({
  query: "How does caching work?",
  repositoryId: repo.id,
  context: {
    component: 'data-layer',
    relatedFiles: ['src/cache/redis.js', 'src/cache/memory.js']
  }
});

// Use conversation for related questions
const conversation = await ccl.conversations.create({ repositoryId: repo.id });
const responses = await Promise.all([
  conversation.ask("What caching strategies are used?"),
  conversation.ask("How is cache invalidation handled?"),
  conversation.ask("What's the cache hit ratio code?")
]);
```

### 3. Pattern Management

```javascript
// Version your patterns
const pattern = await ccl.patterns.create({
  name: 'Repository Pattern v2',
  version: '2.0.0',
  breaking: true,
  migrationGuide: 'See migration.md'
});

// Test patterns before deployment
const testResults = await ccl.patterns.test({
  patternId: pattern.id,
  testCases: [
    { input: 'class UserRepo', expected: true },
    { input: 'function getUser', expected: false }
  ]
});
```

### 4. Error Handling

```javascript
// Implement retry logic
async function reliableQuery(query, options, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await ccl.query(query, options);
    } catch (error) {
      if (error.code === 'RATE_LIMITED' && i < maxRetries - 1) {
        await sleep(error.retryAfter * 1000);
      } else {
        throw error;
      }
    }
  }
}

// Handle partial results
const analysis = await ccl.getAnalysis(analysisId);
if (analysis.status === 'partial') {
  console.warn('Analysis incomplete, results may be limited');
  // Proceed with partial data
}
```

### 5. Security Considerations

```javascript
// Never expose API keys in client-side code
// Bad: API key in browser JavaScript
const ccl = new CCL({ apiKey: 'sk_live_...' }); // DON'T DO THIS

// Good: Proxy through your backend
const response = await fetch('/api/ccl-proxy', {
  method: 'POST',
  body: JSON.stringify({ query: 'How does auth work?' })
});

// Validate webhook signatures
app.post('/webhooks/ccl', (req, res) => {
  const signature = req.headers['x-ccl-signature'];
  const valid = ccl.webhooks.verify(req.body, signature);
  
  if (!valid) {
    return res.status(401).send('Invalid signature');
  }
  
  // Process webhook
});
```

---

## Troubleshooting

### Common Issues

#### Analysis Fails
```javascript
// Check repository accessibility
const validation = await ccl.repositories.validate({
  url: 'https://github.com/private/repo'
});

if (!validation.accessible) {
  console.log('Repository not accessible:', validation.reason);
  // Add authentication or check permissions
}

// Check language support
const supported = await ccl.languages.list();
if (!supported.includes('rust')) {
  console.log('Rust not yet supported');
}
```

#### Slow Performance
```javascript
// Use streaming for large responses
const stream = await ccl.query({
  query: 'Explain the entire architecture',
  repositoryId: repo.id,
  stream: true
});

for await (const chunk of stream) {
  process.stdout.write(chunk);
}

// Paginate large result sets
let page = 1;
let hasMore = true;

while (hasMore) {
  const patterns = await ccl.patterns.list({
    repositoryId: repo.id,
    page: page,
    perPage: 100
  });
  
  processPatterns(patterns.data);
  hasMore = patterns.hasNextPage;
  page++;
}
```

#### Authentication Errors
```bash
# Verify API key is valid
ccl auth check

# Check permissions
ccl auth permissions

# Refresh authentication
ccl auth refresh
```

### Debug Mode

Enable debug mode for detailed logs:

```javascript
const ccl = new CCL({
  apiKey: process.env.CCL_API_KEY,
  debug: true,
  logger: console
});

// Or via environment variable
process.env.CCL_DEBUG = 'true';
```

---

## Migration Guide

### Migrating from Other Tools

#### From Raw AST Analysis
```javascript
// Before: Manual AST parsing
const ast = parser.parse(code);
const patterns = analyzeAST(ast);

// After: CCL intelligent analysis
const patterns = await ccl.patterns.detect({
  code: code,
  language: 'javascript'
});
```

#### From Static Analysis Tools
```javascript
// Before: Rule-based linting
const eslintResults = await eslint.lintFiles(['src/**/*.js']);

// After: Pattern-based validation
const validation = await ccl.patterns.validateDirectory({
  repositoryId: repo.id,
  directory: 'src'
});
```

---

## Resources

### Links
- [API Reference](https://docs.ccl.dev/api)
- [Pattern Library](https://patterns.ccl.dev)
- [Video Tutorials](https://ccl.dev/tutorials)
- [Community Forum](https://community.ccl.dev)
- [GitHub](https://github.com/ccl-platform)

### Support
- Documentation: [docs.ccl.dev](https://docs.ccl.dev)
- Email: <EMAIL>
- Discord: [discord.gg/ccl](https://discord.gg/ccl)
- Stack Overflow: Tag `ccl`

### Stay Updated
- Blog: [blog.ccl.dev](https://blog.ccl.dev)
- Twitter: [@ccldev](https://twitter.com/ccldev)
- Newsletter: [ccl.dev/newsletter](https://ccl.dev/newsletter)

---

## License

CCL SDKs are licensed under the MIT License. See [LICENSE](https://github.com/ccl-platform/ccl-sdk/blob/main/LICENSE) for details.

The CCL platform and API are proprietary. See [Terms of Service](https://ccl.dev/terms) for usage rights.