#!/bin/bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting CCL development environment...${NC}"

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Change to project root
cd "$PROJECT_ROOT"

# Load environment variables
if [ -f .env.development ]; then
    export $(cat .env.development | grep -v '^#' | xargs)
    echo -e "✅ Loaded environment variables from .env.development"
fi

# Check if setup has been run
if [ ! -f credentials/dev-cert.pem ] || [ ! -f .env.development ]; then
    echo -e "${YELLOW}⚠️  Setup has not been completed. Running setup first...${NC}"
    ./scripts/dev/setup.sh
fi

# Function to wait for service
wait_for_service() {
    local service_name=$1
    local service_url=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "Waiting for ${service_name}..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f -o /dev/null "$service_url" 2>/dev/null; then
            echo -e "✅ ${service_name} is ready"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            echo -e "${RED}❌ ${service_name} failed to start${NC}"
            return 1
        fi
        
        sleep 2
        ((attempt++))
    done
}

# Start infrastructure services first
echo -e "\n${YELLOW}Starting infrastructure services...${NC}"
docker-compose -f docker/docker-compose.yml up -d \
    postgres redis spanner-emulator firestore-emulator \
    pubsub-emulator storage-emulator

# Wait for infrastructure to be ready
echo -e "\n${YELLOW}Waiting for infrastructure to be ready...${NC}"
wait_for_service "PostgreSQL" "postgresql://ccl_dev:dev_password@localhost:5432/ccl_local?sslmode=disable" || true
wait_for_service "Redis" "redis://localhost:6379" || true

# Start observability services
echo -e "\n${YELLOW}Starting observability services...${NC}"
docker-compose -f docker/docker-compose.yml up -d \
    otel-collector jaeger prometheus grafana

# Start CCL services
echo -e "\n${YELLOW}Starting CCL services...${NC}"
docker-compose -f docker/docker-compose.yml up -d \
    analysis-engine query-intelligence pattern-mining \
    marketplace collaboration web api-gateway

# Wait for services to be ready
echo -e "\n${YELLOW}Waiting for services to be ready...${NC}"
wait_for_service "API Gateway" "http://localhost:8000/health"
wait_for_service "Grafana" "http://localhost:3000"
wait_for_service "Jaeger" "http://localhost:16686"

# Show status
echo -e "\n${GREEN}✅ CCL development environment is running!${NC}"
echo -e "\n${BLUE}Service Status:${NC}"
docker-compose -f docker/docker-compose.yml ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"

# Show access information
echo -e "\n${BLUE}Access Points:${NC}"
echo -e "┌─────────────────────┬──────────────────────────────┐"
echo -e "│ Service             │ URL                          │"
echo -e "├─────────────────────┼──────────────────────────────┤"
echo -e "│ API Gateway         │ ${YELLOW}http://localhost:8000${NC}        │"
echo -e "│ Web Application     │ ${YELLOW}http://localhost:3001${NC}        │"
echo -e "│ Grafana Dashboard   │ ${YELLOW}http://localhost:3000${NC}        │"
echo -e "│ Jaeger UI           │ ${YELLOW}http://localhost:16686${NC}       │"
echo -e "│ Prometheus          │ ${YELLOW}http://localhost:9090${NC}        │"
echo -e "└─────────────────────┴──────────────────────────────┘"

echo -e "\n${BLUE}Service Endpoints:${NC}"
echo -e "┌─────────────────────┬──────────────────────────────┐"
echo -e "│ Service             │ Direct URL                   │"
echo -e "├─────────────────────┼──────────────────────────────┤"
echo -e "│ Analysis Engine     │ ${YELLOW}http://localhost:8001${NC}        │"
echo -e "│ Query Intelligence  │ ${YELLOW}http://localhost:8002${NC}        │"
echo -e "│ Pattern Mining      │ ${YELLOW}http://localhost:8003${NC}        │"
echo -e "│ Marketplace         │ ${YELLOW}http://localhost:8004${NC}        │"
echo -e "│ Collaboration       │ ${YELLOW}http://localhost:8005${NC}        │"
echo -e "└─────────────────────┴──────────────────────────────┘"

echo -e "\n${BLUE}Infrastructure:${NC}"
echo -e "┌─────────────────────┬──────────────────────────────┐"
echo -e "│ Service             │ Connection String            │"
echo -e "├─────────────────────┼──────────────────────────────┤"
echo -e "│ PostgreSQL          │ ${YELLOW}localhost:5432${NC}               │"
echo -e "│ Redis               │ ${YELLOW}localhost:6379${NC}               │"
echo -e "│ Spanner Emulator    │ ${YELLOW}localhost:9010${NC}               │"
echo -e "│ Firestore Emulator  │ ${YELLOW}localhost:8080${NC}               │"
echo -e "│ Pub/Sub Emulator    │ ${YELLOW}localhost:8085${NC}               │"
echo -e "│ Storage Emulator    │ ${YELLOW}localhost:4443${NC}               │"
echo -e "└─────────────────────┴──────────────────────────────┘"

echo -e "\n${BLUE}Useful Commands:${NC}"
echo -e "View logs:         ${YELLOW}docker-compose -f docker/docker-compose.yml logs -f [service]${NC}"
echo -e "Stop all:          ${YELLOW}./scripts/dev/stop.sh${NC}"
echo -e "Reset environment: ${YELLOW}./scripts/dev/reset.sh${NC}"
echo -e "Seed data:         ${YELLOW}./scripts/dev/seed-data.sh${NC}"
echo -e "Health check:      ${YELLOW}./scripts/dev/health-check.sh${NC}"

echo -e "\n${GREEN}Happy coding! 🎉${NC}"