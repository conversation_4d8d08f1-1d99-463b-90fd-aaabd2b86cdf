#!/bin/bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🏥 CCL Development Environment Health Check${NC}"
echo -e "${BLUE}===========================================${NC}"

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Change to project root
cd "$PROJECT_ROOT"

# Load environment variables
if [ -f .env.development ]; then
    export $(cat .env.development | grep -v '^#' | xargs)
fi

# Track overall health
ALL_HEALTHY=true

# Function to check service health
check_service() {
    local service_name=$1
    local service_url=$2
    local check_type=${3:-"http"}
    
    printf "%-25s" "$service_name:"
    
    case $check_type in
        "http")
            if response=$(curl -s -f -w "HTTP %{http_code} - %{time_total}s" -o /dev/null "$service_url" 2>/dev/null); then
                echo -e "${GREEN}✅ Healthy${NC} ($response)"
            else
                echo -e "${RED}❌ Unhealthy${NC}"
                ALL_HEALTHY=false
            fi
            ;;
        "tcp")
            if nc -z -w2 $(echo "$service_url" | sed 's/:/ /') 2>/dev/null; then
                echo -e "${GREEN}✅ Listening${NC}"
            else
                echo -e "${RED}❌ Not listening${NC}"
                ALL_HEALTHY=false
            fi
            ;;
        "redis")
            if redis-cli -h localhost -p 6379 ping 2>/dev/null | grep -q PONG; then
                echo -e "${GREEN}✅ Healthy${NC}"
            else
                echo -e "${RED}❌ Unhealthy${NC}"
                ALL_HEALTHY=false
            fi
            ;;
        "postgres")
            if PGPASSWORD=dev_password psql -h localhost -U ccl_dev -d ccl_local -c "SELECT 1" &>/dev/null; then
                echo -e "${GREEN}✅ Healthy${NC}"
            else
                echo -e "${RED}❌ Unhealthy${NC}"
                ALL_HEALTHY=false
            fi
            ;;
    esac
}

# Function to check container status
check_container() {
    local container_name=$1
    
    printf "%-25s" "$container_name:"
    
    if status=$(docker ps --filter "name=$container_name" --format "{{.Status}}" 2>/dev/null | head -n1); then
        if [ -n "$status" ]; then
            if [[ "$status" == *"Up"* ]]; then
                echo -e "${GREEN}✅ Running${NC} ($status)"
            else
                echo -e "${YELLOW}⚠️  Status:${NC} $status"
                ALL_HEALTHY=false
            fi
        else
            echo -e "${RED}❌ Not running${NC}"
            ALL_HEALTHY=false
        fi
    else
        echo -e "${RED}❌ Docker error${NC}"
        ALL_HEALTHY=false
    fi
}

# Check Docker daemon
echo -e "\n${YELLOW}Docker Status:${NC}"
if docker info &>/dev/null; then
    echo -e "Docker daemon: ${GREEN}✅ Running${NC}"
else
    echo -e "Docker daemon: ${RED}❌ Not running${NC}"
    echo -e "${RED}Please start Docker and try again.${NC}"
    exit 1
fi

# Check containers
echo -e "\n${YELLOW}Container Status:${NC}"
check_container "ccl-dev-network_postgres_1"
check_container "ccl-dev-network_redis_1"
check_container "ccl-dev-network_spanner-emulator_1"
check_container "ccl-dev-network_firestore-emulator_1"
check_container "ccl-dev-network_pubsub-emulator_1"
check_container "ccl-dev-network_storage-emulator_1"
check_container "ccl-dev-network_analysis-engine_1"
check_container "ccl-dev-network_query-intelligence_1"
check_container "ccl-dev-network_pattern-mining_1"
check_container "ccl-dev-network_marketplace_1"
check_container "ccl-dev-network_collaboration_1"
check_container "ccl-dev-network_web_1"
check_container "ccl-dev-network_api-gateway_1"

# Check service endpoints
echo -e "\n${YELLOW}Service Health:${NC}"
check_service "API Gateway" "http://localhost:8000/health" "http"
check_service "Analysis Engine" "http://localhost:8001/health" "http"
check_service "Query Intelligence" "http://localhost:8002/health" "http"
check_service "Pattern Mining" "http://localhost:8003/health" "http"
check_service "Marketplace" "http://localhost:8004/health" "http"
check_service "Collaboration" "http://localhost:8005/health" "http"
check_service "Web Application" "http://localhost:3001" "http"

# Check infrastructure services
echo -e "\n${YELLOW}Infrastructure Health:${NC}"
check_service "PostgreSQL" "localhost:5432" "postgres"
check_service "Redis" "localhost:6379" "redis"
check_service "Spanner Emulator" "localhost:9010" "tcp"
check_service "Firestore Emulator" "localhost:8080" "tcp"
check_service "Pub/Sub Emulator" "localhost:8085" "tcp"
check_service "Storage Emulator" "localhost:4443" "tcp"

# Check observability stack
echo -e "\n${YELLOW}Observability Stack:${NC}"
check_service "Prometheus" "http://localhost:9090/-/healthy" "http"
check_service "Grafana" "http://localhost:3000/api/health" "http"
check_service "Jaeger" "http://localhost:16686" "http"
check_service "OTEL Collector" "http://localhost:13133" "http"

# Check resource usage
echo -e "\n${YELLOW}Resource Usage:${NC}"
if command -v docker &> /dev/null; then
    echo -e "\n${BLUE}Container Resources:${NC}"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -20
fi

# Database connection check
echo -e "\n${YELLOW}Database Connectivity:${NC}"
if PGPASSWORD=dev_password psql -h localhost -U ccl_dev -d ccl_local -c "SELECT COUNT(*) as repositories FROM analysis.repositories;" 2>/dev/null; then
    echo -e "PostgreSQL queries: ${GREEN}✅ Working${NC}"
else
    echo -e "PostgreSQL queries: ${RED}❌ Failed${NC}"
fi

# Network connectivity between services
echo -e "\n${YELLOW}Inter-Service Connectivity:${NC}"
# This would need to be run from within a container to be accurate
echo -e "Note: Full inter-service connectivity can be tested by running seed-data.sh"

# Summary
echo -e "\n${BLUE}===========================================${NC}"
if [ "$ALL_HEALTHY" = true ]; then
    echo -e "${GREEN}✅ All services are healthy!${NC}"
    echo -e "\nYou can access:"
    echo -e "  - API Gateway: ${YELLOW}http://localhost:8000${NC}"
    echo -e "  - Web App: ${YELLOW}http://localhost:3001${NC}"
    echo -e "  - Grafana: ${YELLOW}http://localhost:3000${NC} (admin/admin)"
    echo -e "  - Jaeger: ${YELLOW}http://localhost:16686${NC}"
else
    echo -e "${RED}❌ Some services are unhealthy!${NC}"
    echo -e "\nTroubleshooting steps:"
    echo -e "1. Check container logs: ${YELLOW}docker-compose -f docker/docker-compose.yml logs [service-name]${NC}"
    echo -e "2. Restart services: ${YELLOW}./scripts/dev/stop.sh && ./scripts/dev/start.sh${NC}"
    echo -e "3. Reset environment: ${YELLOW}./scripts/dev/reset.sh${NC}"
    echo -e "4. Check Docker resources: ${YELLOW}docker system df${NC}"
fi

# Optional detailed mode
if [ "${1:-}" == "-v" ] || [ "${1:-}" == "--verbose" ]; then
    echo -e "\n${YELLOW}Detailed Service Information:${NC}"
    
    # Get service versions
    echo -e "\n${BLUE}Service Versions:${NC}"
    curl -s http://localhost:8001/health 2>/dev/null | jq -r '. | "Analysis Engine: \(.version)"' || echo "Analysis Engine: Unable to fetch"
    curl -s http://localhost:8002/health 2>/dev/null | jq -r '. | "Query Intelligence: \(.version)"' || echo "Query Intelligence: Unable to fetch"
    curl -s http://localhost:8003/health 2>/dev/null | jq -r '. | "Pattern Mining: \(.version)"' || echo "Pattern Mining: Unable to fetch"
    curl -s http://localhost:8004/health 2>/dev/null | jq -r '. | "Marketplace: \(.version)"' || echo "Marketplace: Unable to fetch"
    
    # Show recent container logs
    echo -e "\n${BLUE}Recent Errors (last 10 lines):${NC}"
    docker-compose -f docker/docker-compose.yml logs --tail=10 2>&1 | grep -i error || echo "No recent errors found"
fi