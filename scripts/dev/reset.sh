#!/bin/bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🔄 Resetting CCL development environment...${NC}"

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Change to project root
cd "$PROJECT_ROOT"

# Confirmation prompt
echo -e "${RED}⚠️  WARNING: This will remove all local data and reset the environment!${NC}"
echo -e "This includes:"
echo -e "  - All database data"
echo -e "  - All cached data"
echo -e "  - All log files"
echo -e "  - All uploaded files"
echo -e ""
read -p "Are you sure you want to continue? (y/N) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Reset cancelled.${NC}"
    exit 0
fi

# Stop everything with volume removal
echo -e "\n${YELLOW}Stopping all services and removing volumes...${NC}"
./scripts/dev/stop.sh --volumes

# Remove additional data directories
echo -e "\n${YELLOW}Removing data directories...${NC}"
rm -rf data/postgres data/redis data/spanner data/firestore data/storage
rm -rf logs/*

# Remove generated certificates
echo -e "\n${YELLOW}Removing development certificates...${NC}"
rm -f credentials/dev-cert.pem credentials/dev-key.pem

# Remove environment file
echo -e "\n${YELLOW}Removing environment file...${NC}"
rm -f .env.development

# Clean up Docker system
echo -e "\n${YELLOW}Cleaning up Docker system...${NC}"
docker system prune -f

# Run setup again
echo -e "\n${YELLOW}Running setup to recreate environment...${NC}"
./scripts/dev/setup.sh

echo -e "\n${GREEN}✅ CCL development environment has been reset${NC}"
echo -e "\nTo start fresh:"
echo -e "  ${YELLOW}./scripts/dev/start.sh${NC}"