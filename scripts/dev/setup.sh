#!/bin/bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Setting up CCL development environment...${NC}"

# Check prerequisites
check_prerequisites() {
    echo -e "\n${YELLOW}Checking prerequisites...${NC}"
    
    local missing_tools=()
    
    # Docker
    if ! command -v docker &> /dev/null; then
        missing_tools+=("Docker")
    else
        echo -e "✅ Docker $(docker --version | awk '{print $3}')"
    fi
    
    # Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        missing_tools+=("Docker Compose")
    else
        echo -e "✅ Docker Compose $(docker-compose --version 2>/dev/null || docker compose version | grep version)"
    fi
    
    # Git
    if ! command -v git &> /dev/null; then
        missing_tools+=("Git")
    else
        echo -e "✅ Git $(git --version | awk '{print $3}')"
    fi
    
    # Optional but recommended tools
    echo -e "\n${YELLOW}Checking optional tools...${NC}"
    
    # Languages
    for cmd in rustc go python3 node; do
        if command -v $cmd &> /dev/null; then
            case $cmd in
                rustc)
                    echo -e "✅ Rust $(rustc --version | awk '{print $2}')"
                    ;;
                go)
                    echo -e "✅ Go $(go version | awk '{print $3}')"
                    ;;
                python3)
                    echo -e "✅ Python $(python3 --version | awk '{print $2}')"
                    ;;
                node)
                    echo -e "✅ Node.js $(node --version)"
                    ;;
            esac
        else
            echo -e "⚠️  $cmd not installed. Some features may not work locally."
        fi
    done
    
    # Check if any required tools are missing
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo -e "\n${RED}❌ Missing required tools: ${missing_tools[*]}${NC}"
        echo -e "Please install these tools and run setup again."
        exit 1
    fi
    
    echo -e "\n${GREEN}✅ Prerequisites check passed${NC}"
}

# Create necessary directories
create_directories() {
    echo -e "\n${YELLOW}Creating directories...${NC}"
    
    # Service directories (will be created when services are implemented)
    mkdir -p services/{analysis-engine,query-intelligence,pattern-mining,marketplace,collaboration,web}
    
    # Credentials directory
    mkdir -p credentials
    
    # Logs directory
    mkdir -p logs
    
    # Data directories for local development
    mkdir -p data/{postgres,redis,spanner,firestore,storage}
    
    echo -e "✅ Directories created"
}

# Generate development certificates
generate_certs() {
    echo -e "\n${YELLOW}Generating development certificates...${NC}"
    
    if [ ! -f credentials/dev-cert.pem ]; then
        openssl req -x509 -newkey rsa:4096 -nodes \
            -keyout credentials/dev-key.pem \
            -out credentials/dev-cert.pem \
            -days 365 \
            -subj "/C=US/ST=State/L=City/O=CCL/CN=*.ccl.local" \
            2>/dev/null
        echo -e "✅ Development certificates generated"
    else
        echo -e "✅ Development certificates already exist"
    fi
}

# Setup local DNS
setup_local_dns() {
    echo -e "\n${YELLOW}Setting up local DNS...${NC}"
    
    if ! grep -q "ccl.local" /etc/hosts; then
        echo -e "Adding CCL domains to /etc/hosts (requires sudo)..."
        sudo tee -a /etc/hosts << EOF

# CCL Development
127.0.0.1 api.ccl.local
127.0.0.1 app.ccl.local
127.0.0.1 storage.ccl.local
127.0.0.1 auth.ccl.local
127.0.0.1 ws.ccl.local
EOF
        echo -e "✅ Local DNS configured"
    else
        echo -e "✅ Local DNS already configured"
    fi
}

# Create environment file
create_env_file() {
    echo -e "\n${YELLOW}Creating .env.development file...${NC}"
    
    if [ ! -f .env.development ]; then
        cat > .env.development << 'EOF'
# CCL Development Environment
ENV=development
PROJECT_ID=ccl-local

# Database
DATABASE_URL=postgresql://ccl_dev:dev_password@localhost:5432/ccl_local
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=ccl_local
DATABASE_USER=ccl_dev
DATABASE_PASSWORD=dev_password

# Redis
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# GCP Emulators
SPANNER_EMULATOR_HOST=localhost:9010
FIRESTORE_EMULATOR_HOST=localhost:8080
PUBSUB_EMULATOR_HOST=localhost:8085
STORAGE_EMULATOR_HOST=http://localhost:4443

# Service URLs
ANALYSIS_SERVICE_URL=http://localhost:8001
QUERY_SERVICE_URL=http://localhost:8002
PATTERN_SERVICE_URL=http://localhost:8003
MARKETPLACE_SERVICE_URL=http://localhost:8004
COLLABORATION_SERVICE_URL=http://localhost:8005
WEB_SERVICE_URL=http://localhost:3001

# API Gateway
API_GATEWAY_URL=http://localhost:8000

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:8005

# Observability
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
JAEGER_UI_URL=http://localhost:16686
GRAFANA_URL=http://localhost:3000
PROMETHEUS_URL=http://localhost:9090

# Development Keys (DO NOT USE IN PRODUCTION)
JWT_SECRET=dev_jwt_secret_change_me_in_production
ENCRYPTION_KEY=dev_encryption_key_32_bytes_long!!
API_KEY=dev_api_key_for_testing
STRIPE_SECRET_KEY=sk_test_dummy_key_for_development
VERTEX_AI_API_KEY=dummy_key_for_local_dev

# Feature Flags
ENABLE_DEBUG_LOGGING=true
ENABLE_HOT_RELOAD=true
ENABLE_MOCK_SERVICES=true
ENABLE_DEV_TOOLS=true

# Resource Limits
MAX_UPLOAD_SIZE=50MB
MAX_ANALYSIS_TIME=300s
MAX_CONCURRENT_ANALYSES=10
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60s
EOF
        echo -e "✅ Environment file created"
    else
        echo -e "✅ Environment file already exists"
    fi
}

# Create service placeholder files
create_service_placeholders() {
    echo -e "\n${YELLOW}Creating service placeholders...${NC}"
    
    # Analysis Engine (Rust)
    if [ ! -f services/analysis-engine/Dockerfile.dev ]; then
        cat > services/analysis-engine/Dockerfile.dev << 'EOF'
FROM rust:1.75-slim

RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install cargo-watch for hot reloading
RUN cargo install cargo-watch

# Copy placeholder files
COPY Cargo.toml ./
RUN mkdir src && echo 'fn main() { println!("Analysis Engine placeholder"); }' > src/main.rs

CMD ["cargo", "watch", "-x", "run"]
EOF
    fi
    
    # Create minimal Cargo.toml if it doesn't exist
    if [ ! -f services/analysis-engine/Cargo.toml ]; then
        cat > services/analysis-engine/Cargo.toml << 'EOF'
[package]
name = "analysis-engine"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1", features = ["full"] }
axum = "0.7"
EOF
    fi
    
    # Query Intelligence (Python)
    if [ ! -f services/query-intelligence/Dockerfile.dev ]; then
        cat > services/query-intelligence/Dockerfile.dev << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy placeholder files
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt || echo "No requirements.txt yet"

# Create placeholder app
RUN echo 'print("Query Intelligence placeholder")' > main.py

CMD ["python", "-m", "uvicorn", "main:app", "--reload", "--host", "0.0.0.0", "--port", "8002"]
EOF
    fi
    
    # Pattern Mining (Python)
    if [ ! -f services/pattern-mining/Dockerfile.dev ]; then
        cat > services/pattern-mining/Dockerfile.dev << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy placeholder files
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt || echo "No requirements.txt yet"

# Create placeholder app
RUN echo 'print("Pattern Mining placeholder")' > main.py

CMD ["python", "-m", "uvicorn", "main:app", "--reload", "--host", "0.0.0.0", "--port", "8003"]
EOF
    fi
    
    # Marketplace (Go)
    if [ ! -f services/marketplace/Dockerfile.dev ]; then
        cat > services/marketplace/Dockerfile.dev << 'EOF'
FROM golang:1.21-alpine

RUN apk add --no-cache git

WORKDIR /app

# Install air for hot reloading
RUN go install github.com/cosmtrek/air@latest

# Copy placeholder files
COPY go.mod ./
RUN go mod download || echo "No go.mod yet"

# Create placeholder app
RUN echo 'package main; import "fmt"; func main() { fmt.Println("Marketplace placeholder") }' > main.go

CMD ["air", "-c", ".air.toml"]
EOF
    fi
    
    # Collaboration (TypeScript)
    if [ ! -f services/collaboration/Dockerfile.dev ]; then
        cat > services/collaboration/Dockerfile.dev << 'EOF'
FROM node:20-slim

WORKDIR /app

# Copy placeholder files
COPY package.json ./
RUN npm install || echo "No package.json yet"

# Create placeholder app
RUN echo 'console.log("Collaboration placeholder")' > index.js

CMD ["npm", "run", "dev"]
EOF
    fi
    
    # Web (TypeScript/Next.js)
    if [ ! -f services/web/Dockerfile.dev ]; then
        cat > services/web/Dockerfile.dev << 'EOF'
FROM node:20-slim

WORKDIR /app

# Copy placeholder files
COPY package.json ./
RUN npm install || echo "No package.json yet"

# Create placeholder app
RUN echo 'console.log("Web placeholder")' > index.js

CMD ["npm", "run", "dev"]
EOF
    fi
    
    echo -e "✅ Service placeholders created"
}

# Create Grafana provisioning
create_grafana_provisioning() {
    echo -e "\n${YELLOW}Creating Grafana provisioning...${NC}"
    
    mkdir -p docker/config/grafana/provisioning/{dashboards,datasources}
    
    # Datasource configuration
    cat > docker/config/grafana/provisioning/datasources/prometheus.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF
    
    # Dashboard provisioning
    cat > docker/config/grafana/provisioning/dashboards/dashboards.yml << 'EOF'
apiVersion: 1

providers:
  - name: 'CCL Dashboards'
    orgId: 1
    folder: ''
    folderUid: ''
    type: file
    disableDeletion: false
    editable: true
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF
    
    echo -e "✅ Grafana provisioning created"
}

# Pull Docker images
pull_docker_images() {
    echo -e "\n${YELLOW}Pulling Docker images...${NC}"
    echo -e "This may take a few minutes on first run..."
    
    docker-compose -f docker/docker-compose.yml pull || true
    
    echo -e "✅ Docker images ready"
}

# Main setup flow
main() {
    # Get the script directory
    SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    check_prerequisites
    create_directories
    generate_certs
    setup_local_dns
    create_env_file
    create_service_placeholders
    create_grafana_provisioning
    pull_docker_images
    
    echo -e "\n${GREEN}✨ CCL development environment setup complete!${NC}"
    echo -e "\nTo start the development environment:"
    echo -e "  ${YELLOW}./scripts/dev/start.sh${NC}"
    echo -e "\nAccess points:"
    echo -e "  - API Gateway: ${YELLOW}http://localhost:8000${NC}"
    echo -e "  - Web App: ${YELLOW}http://localhost:3001${NC}"
    echo -e "  - Grafana: ${YELLOW}http://localhost:3000${NC} (admin/admin)"
    echo -e "  - Jaeger: ${YELLOW}http://localhost:16686${NC}"
    echo -e "  - Prometheus: ${YELLOW}http://localhost:9090${NC}"
    echo -e "\nDevelopment credentials are in ${YELLOW}.env.development${NC}"
}

main "$@"