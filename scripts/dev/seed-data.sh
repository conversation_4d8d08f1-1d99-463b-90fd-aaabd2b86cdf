#!/bin/bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🌱 Seeding development data...${NC}"

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Change to project root
cd "$PROJECT_ROOT"

# Load environment variables
if [ -f .env.development ]; then
    export $(cat .env.development | grep -v '^#' | xargs)
fi

# Base URL for API
API_URL="${API_GATEWAY_URL:-http://localhost:8000}"

# Function to make API call and check response
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "\n${YELLOW}${description}...${NC}"
    
    response=$(curl -s -w "\n%{http_code}" -X "$method" \
        -H "Content-Type: application/json" \
        -d "$data" \
        "${API_URL}${endpoint}" 2>/dev/null || true)
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')
    
    if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
        echo -e "✅ Success (HTTP $http_code)"
        if [ -n "$body" ]; then
            echo "$body" | jq '.' 2>/dev/null || echo "$body"
        fi
    else
        echo -e "${RED}❌ Failed (HTTP $http_code)${NC}"
        if [ -n "$body" ]; then
            echo "$body" | jq '.' 2>/dev/null || echo "$body"
        fi
    fi
}

# Wait for services to be ready
echo -e "${YELLOW}Waiting for services to be ready...${NC}"
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -s -f -o /dev/null "${API_URL}/health" 2>/dev/null; then
        echo -e "✅ API Gateway is ready"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo -e "${RED}❌ Services are not ready. Please ensure they are running.${NC}"
        exit 1
    fi
    
    sleep 2
    ((attempt++))
done

# Seed repositories for analysis
echo -e "\n${BLUE}=== Seeding Repositories ===${NC}"

api_call "POST" "/api/v1/analysis/repositories" \
    '{"url": "https://github.com/golang/go", "name": "Go Programming Language", "branch": "master"}' \
    "Adding Go repository"

api_call "POST" "/api/v1/analysis/repositories" \
    '{"url": "https://github.com/rust-lang/rust", "name": "Rust Programming Language", "branch": "master"}' \
    "Adding Rust repository"

api_call "POST" "/api/v1/analysis/repositories" \
    '{"url": "https://github.com/facebook/react", "name": "React Framework", "branch": "main"}' \
    "Adding React repository"

api_call "POST" "/api/v1/analysis/repositories" \
    '{"url": "https://github.com/tensorflow/tensorflow", "name": "TensorFlow", "branch": "master"}' \
    "Adding TensorFlow repository"

# Trigger analysis for one repository
echo -e "\n${BLUE}=== Triggering Analysis ===${NC}"

api_call "POST" "/api/v1/analysis/analyze" \
    '{"repository_id": "repo_1", "full_analysis": true}' \
    "Starting analysis for Go repository"

# Create sample patterns
echo -e "\n${BLUE}=== Creating Sample Patterns ===${NC}"

api_call "POST" "/api/v1/patterns/detect" \
    '{"repository_id": "repo_1", "pattern_types": ["singleton", "factory", "observer"]}' \
    "Detecting patterns in Go repository"

# Create marketplace patterns
echo -e "\n${BLUE}=== Creating Marketplace Patterns ===${NC}"

api_call "POST" "/api/v1/marketplace/patterns" \
    '{
        "name": "Singleton Pattern Detector",
        "description": "Detects singleton pattern implementations across multiple languages",
        "category": "design_patterns",
        "price": 9.99,
        "tags": ["design-pattern", "singleton", "best-practices"]
    }' \
    "Creating Singleton pattern"

api_call "POST" "/api/v1/marketplace/patterns" \
    '{
        "name": "Security Vulnerability Scanner",
        "description": "Identifies common security vulnerabilities in code",
        "category": "security",
        "price": 29.99,
        "tags": ["security", "vulnerability", "scanning"]
    }' \
    "Creating Security scanner"

api_call "POST" "/api/v1/marketplace/patterns" \
    '{
        "name": "Performance Bottleneck Detector",
        "description": "Finds performance bottlenecks and suggests optimizations",
        "category": "performance",
        "price": 19.99,
        "tags": ["performance", "optimization", "profiling"]
    }' \
    "Creating Performance detector"

# Create sample queries
echo -e "\n${BLUE}=== Creating Sample Queries ===${NC}"

api_call "POST" "/api/v1/query/ask" \
    '{
        "repository_id": "repo_1",
        "query": "How does the Go scheduler work?",
        "context": "runtime"
    }' \
    "Asking about Go scheduler"

api_call "POST" "/api/v1/query/ask" \
    '{
        "repository_id": "repo_1",
        "query": "Show me examples of concurrent programming patterns",
        "context": "concurrency"
    }' \
    "Asking about concurrency patterns"

# Seed collaboration sessions
echo -e "\n${BLUE}=== Creating Collaboration Sessions ===${NC}"

# Note: Collaboration endpoints might require WebSocket, so we'll use HTTP endpoints if available
api_call "POST" "/api/v1/collaboration/sessions" \
    '{
        "name": "Code Review Session",
        "repository_id": "repo_1",
        "participants": ["<EMAIL>", "<EMAIL>"]
    }' \
    "Creating collaboration session"

# Generate some metrics data
echo -e "\n${BLUE}=== Generating Metrics Data ===${NC}"

# Make multiple API calls to generate metrics
for i in {1..10}; do
    curl -s -o /dev/null "${API_URL}/health" &
    curl -s -o /dev/null "${API_URL}/api/v1/analysis/health" &
    curl -s -o /dev/null "${API_URL}/api/v1/query/health" &
done
wait

echo -e "\n${GREEN}✅ Development data seeding complete!${NC}"

# Show summary
echo -e "\n${BLUE}Summary:${NC}"
echo -e "- Repositories added: 4"
echo -e "- Analysis triggered: 1"
echo -e "- Marketplace patterns: 3"
echo -e "- Sample queries: 2"
echo -e "- Collaboration sessions: 1"

echo -e "\n${YELLOW}You can now:${NC}"
echo -e "- View repositories at: ${YELLOW}${API_URL}/api/v1/analysis/repositories${NC}"
echo -e "- Check analysis status at: ${YELLOW}${API_URL}/api/v1/analysis/status${NC}"
echo -e "- Browse patterns at: ${YELLOW}${API_URL}/api/v1/marketplace/patterns${NC}"
echo -e "- View metrics at: ${YELLOW}http://localhost:3000${NC} (Grafana)"

# SQL to directly seed PostgreSQL if API is not ready
if [ "$1" == "--sql" ] || [ "$http_code" != "200" ]; then
    echo -e "\n${YELLOW}Seeding directly via SQL...${NC}"
    
    PGPASSWORD=dev_password psql -h localhost -U ccl_dev -d ccl_local << EOF
-- More sample repositories
INSERT INTO analysis.repositories (url, name, status) VALUES
    ('https://github.com/kubernetes/kubernetes', 'kubernetes/kubernetes', 'pending'),
    ('https://github.com/apache/spark', 'apache/spark', 'pending'),
    ('https://github.com/elastic/elasticsearch', 'elastic/elasticsearch', 'pending'),
    ('https://github.com/pytorch/pytorch', 'pytorch/pytorch', 'pending')
ON CONFLICT DO NOTHING;

-- More sample patterns
INSERT INTO patterns.detected_patterns (repository_id, pattern_type, confidence, file_path) VALUES
    (1, 'builder', 0.91, 'src/database/sql/sql.go'),
    (1, 'adapter', 0.89, 'src/net/http/client.go'),
    (2, 'iterator', 0.93, 'library/core/src/iter/mod.rs'),
    (2, 'visitor', 0.86, 'compiler/rustc_ast/src/visit.rs'),
    (3, 'observer', 0.94, 'packages/react/src/ReactHooks.js'),
    (3, 'decorator', 0.88, 'packages/react/src/ReactElement.js')
ON CONFLICT DO NOTHING;

SELECT 'Direct SQL seeding completed' as status;
EOF
fi