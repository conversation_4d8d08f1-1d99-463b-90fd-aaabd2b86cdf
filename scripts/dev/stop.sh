#!/bin/bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🛑 Stopping CCL development environment...${NC}"

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Change to project root
cd "$PROJECT_ROOT"

# Parse command line arguments
REMOVE_VOLUMES=false
REMOVE_IMAGES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--volumes)
            REMOVE_VOLUMES=true
            shift
            ;;
        -i|--images)
            REMOVE_IMAGES=true
            shift
            ;;
        -a|--all)
            REMOVE_VOLUMES=true
            REMOVE_IMAGES=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -v, --volumes    Remove data volumes (database data will be lost)"
            echo "  -i, --images     Remove Docker images"
            echo "  -a, --all        Remove everything (volumes and images)"
            echo "  -h, --help       Show this help message"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Stop all services
echo -e "Stopping all services..."
docker-compose -f docker/docker-compose.yml down

# Remove volumes if requested
if [ "$REMOVE_VOLUMES" = true ]; then
    echo -e "\n${YELLOW}Removing data volumes...${NC}"
    docker-compose -f docker/docker-compose.yml down -v
    echo -e "✅ Data volumes removed"
fi

# Remove images if requested
if [ "$REMOVE_IMAGES" = true ]; then
    echo -e "\n${YELLOW}Removing Docker images...${NC}"
    docker-compose -f docker/docker-compose.yml down --rmi local
    echo -e "✅ Docker images removed"
fi

# Clean up any orphaned containers
echo -e "\n${YELLOW}Cleaning up orphaned containers...${NC}"
docker container prune -f 2>/dev/null || true

echo -e "\n${GREEN}✅ CCL development environment stopped${NC}"

# Show cleanup options if not used
if [ "$REMOVE_VOLUMES" = false ] || [ "$REMOVE_IMAGES" = false ]; then
    echo -e "\n${YELLOW}Additional cleanup options:${NC}"
    if [ "$REMOVE_VOLUMES" = false ]; then
        echo -e "  Remove data volumes: ${YELLOW}$0 --volumes${NC}"
    fi
    if [ "$REMOVE_IMAGES" = false ]; then
        echo -e "  Remove Docker images: ${YELLOW}$0 --images${NC}"
    fi
    echo -e "  Remove everything: ${YELLOW}$0 --all${NC}"
fi