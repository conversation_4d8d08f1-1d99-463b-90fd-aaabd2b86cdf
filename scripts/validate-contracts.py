#!/usr/bin/env python3
"""
Validate JSON Schema contracts for service integration.
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Tuple

import jsonschema
from jsonschema import Draft7Validator, ValidationError


def load_schema(schema_path: Path) -> Dict:
    """Load a JSON schema from file."""
    with open(schema_path, 'r') as f:
        return json.load(f)


def validate_schema_format(schema: Dict, schema_path: Path) -> List[str]:
    """Validate that the schema itself is valid JSON Schema."""
    errors = []
    
    # Check for required top-level properties
    required_props = ['$schema', '$id', 'title', 'type']
    for prop in required_props:
        if prop not in schema:
            errors.append(f"{schema_path}: Missing required property '{prop}'")
    
    # Validate against JSON Schema meta-schema
    try:
        Draft7Validator.check_schema(schema)
    except jsonschema.SchemaError as e:
        errors.append(f"{schema_path}: Invalid schema format - {e.message}")
    
    return errors


def validate_schema_consistency(schemas_dir: Path) -> List[str]:
    """Check consistency across all schemas."""
    errors = []
    schemas = {}
    
    # Load all schemas
    for schema_file in schemas_dir.glob("*.json"):
        schema = load_schema(schema_file)
        schemas[schema_file.stem] = schema
    
    # Check version consistency
    versions = set()
    for name, schema in schemas.items():
        if 'version' in schema:
            versions.add(schema['version'])
    
    if len(versions) > 1:
        errors.append(f"Inconsistent schema versions found: {versions}")
    
    # Check that error response schema is referenced
    if 'error-response-v1' in schemas:
        for name, schema in schemas.items():
            if name != 'error-response-v1' and 'error' not in str(schema):
                errors.append(f"{name}: Should reference error-response schema")
    
    return errors


def validate_examples(schemas_dir: Path, examples_dir: Path) -> List[str]:
    """Validate example files against their schemas."""
    errors = []
    
    if not examples_dir.exists():
        errors.append(f"Examples directory not found: {examples_dir}")
        return errors
    
    # Map example files to schemas
    example_mapping = {
        'ast-output-example.json': 'ast-output-v1.json',
        'query-context-example.json': 'query-context-v1.json',
        'pattern-detection-flow.json': 'pattern-output-v1.json',
        'marketplace-integration-example.json': 'marketplace-pattern-v1.json',
    }
    
    for example_file, schema_file in example_mapping.items():
        example_path = examples_dir / example_file
        schema_path = schemas_dir / schema_file
        
        if not example_path.exists():
            errors.append(f"Example file not found: {example_path}")
            continue
        
        if not schema_path.exists():
            errors.append(f"Schema file not found for example: {schema_path}")
            continue
        
        # Load and validate
        try:
            example_data = json.load(open(example_path))
            schema = load_schema(schema_path)
            validator = Draft7Validator(schema)
            
            validation_errors = list(validator.iter_errors(example_data))
            for error in validation_errors:
                errors.append(f"{example_file}: {error.message} at {'.'.join(str(p) for p in error.path)}")
                
        except json.JSONDecodeError as e:
            errors.append(f"{example_file}: Invalid JSON - {e}")
        except Exception as e:
            errors.append(f"{example_file}: Validation error - {e}")
    
    return errors


def check_schema_references(schema: Dict, schema_path: Path, all_schemas: Dict[str, Path]) -> List[str]:
    """Check that all $ref references point to existing schemas."""
    errors = []
    
    def check_refs(obj, path=""):
        if isinstance(obj, dict):
            if "$ref" in obj:
                ref = obj["$ref"]
                # Check external references
                if ref.startswith("http"):
                    # Extract schema name from URL
                    schema_name = ref.split("/")[-1].replace(".json", "")
                    if schema_name not in all_schemas:
                        errors.append(f"{schema_path}: Referenced schema '{schema_name}' not found at {path}")
                
            for key, value in obj.items():
                check_refs(value, f"{path}.{key}")
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                check_refs(item, f"{path}[{i}]")
    
    check_refs(schema)
    return errors


def main():
    """Main validation function."""
    contracts_dir = Path(__file__).parent.parent / "contracts"
    schemas_dir = contracts_dir / "schemas"
    examples_dir = contracts_dir / "examples"
    
    if not schemas_dir.exists():
        print(f"ERROR: Schemas directory not found: {schemas_dir}")
        sys.exit(1)
    
    all_errors = []
    all_schemas = {f.stem: f for f in schemas_dir.glob("*.json")}
    
    # Validate each schema
    for schema_file in schemas_dir.glob("*.json"):
        print(f"Validating {schema_file.name}...")
        
        try:
            schema = load_schema(schema_file)
            
            # Validate schema format
            errors = validate_schema_format(schema, schema_file)
            all_errors.extend(errors)
            
            # Check references
            ref_errors = check_schema_references(schema, schema_file, all_schemas)
            all_errors.extend(ref_errors)
            
        except json.JSONDecodeError as e:
            all_errors.append(f"{schema_file}: Invalid JSON - {e}")
        except Exception as e:
            all_errors.append(f"{schema_file}: Error loading schema - {e}")
    
    # Validate consistency
    print("Checking schema consistency...")
    consistency_errors = validate_schema_consistency(schemas_dir)
    all_errors.extend(consistency_errors)
    
    # Validate examples
    print("Validating examples...")
    example_errors = validate_examples(schemas_dir, examples_dir)
    all_errors.extend(example_errors)
    
    # Report results
    if all_errors:
        print("\n❌ Validation failed with the following errors:")
        for error in all_errors:
            print(f"  - {error}")
        sys.exit(1)
    else:
        print("\n✅ All schemas validated successfully!")
        sys.exit(0)


if __name__ == "__main__":
    main()