#!/bin/bash
# Validate Current Work Command
# Runs validation commands for current task or all relevant validations

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CLAUDE_DIR="$PROJECT_ROOT/.claude"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"; }
info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1" >&2; }

# Change to project root
cd "$PROJECT_ROOT"

# Run a validation command and capture results
run_validation() {
    local cmd="$1"
    local name="$2"
    
    echo ""
    info "🧪 Running: $name"
    echo "Command: $cmd"
    echo ""
    
    local start_time=$(date +%s)
    local exit_code=0
    
    # Run command and capture output
    if eval "$cmd" 2>&1; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        echo ""
        log "✅ PASSED: $name (${duration}s)"
        return 0
    else
        exit_code=$?
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        echo ""
        error "❌ FAILED: $name (${duration}s, exit code: $exit_code)"
        return $exit_code
    fi
}

# Get validation commands for current focused task
get_focused_task_validations() {
    local current_session
    current_session=$("$CLAUDE_DIR/scripts/session_manager.sh" current 2>/dev/null || echo "")
    
    if [[ -n "$current_session" && "$current_session" != "No current session" ]]; then
        local task_focus_file="$CLAUDE_DIR/memory/sessions/${current_session}_task_focus.json"
        
        if [[ -f "$task_focus_file" ]]; then
            jq -r '.validation_commands[]?' "$task_focus_file" 2>/dev/null || echo ""
        fi
    fi
}

# Get all available validation commands from TASK.md
get_all_validation_commands() {
    if [[ ! -f "TASK.md" ]]; then
        return 1
    fi
    
    # Extract all validation command blocks
    sed -n '/```bash/,/```/p' TASK.md | \
    grep -v '```' | \
    grep -E '^\s*(make|npm|python|cargo|go|docker)' | \
    sed 's/^\s*//' | \
    sort -u
}

# Get CCL system validation commands
get_system_validations() {
    cat << 'EOF'
python3 .claude/scripts/validate_context_system.py
python3 .claude/scripts/test_integrations.py
EOF
}

# Run focused task validations
run_focused_validations() {
    local commands
    commands=$(get_focused_task_validations)
    
    if [[ -z "$commands" ]]; then
        warn "No focused task found or no validation commands defined"
        return 1
    fi
    
    local current_session
    current_session=$("$CLAUDE_DIR/scripts/session_manager.sh" current 2>/dev/null || echo "")
    
    if [[ -n "$current_session" && "$current_session" != "No current session" ]]; then
        local task_focus_file="$CLAUDE_DIR/memory/sessions/${current_session}_task_focus.json"
        local focused_task
        focused_task=$(jq -r '.focused_task // "Unknown"' "$task_focus_file" 2>/dev/null || echo "Unknown")
        
        echo "🎯 Running validations for focused task: $focused_task"
    fi
    
    local total=0
    local passed=0
    local failed=0
    
    while IFS= read -r cmd; do
        if [[ -n "$cmd" ]]; then
            total=$((total + 1))
            if run_validation "$cmd" "Task Validation $total"; then
                passed=$((passed + 1))
            else
                failed=$((failed + 1))
            fi
        fi
    done <<< "$commands"
    
    # Show summary
    echo ""
    echo "=================================================="
    info "📊 FOCUSED TASK VALIDATION SUMMARY"
    echo "=================================================="
    echo "Total Commands: $total"
    echo "Passed: $passed"
    echo "Failed: $failed"
    
    if [[ $failed -eq 0 ]]; then
        log "🎉 All focused task validations PASSED!"
        return 0
    else
        error "❌ $failed validation(s) FAILED!"
        return 1
    fi
}

# Run all available validations
run_all_validations() {
    echo "🔍 Running all available validations..."
    
    local total=0
    local passed=0
    local failed=0
    
    # 1. Run CCL system validations
    info "🏗️  CCL System Validations"
    local system_commands
    system_commands=$(get_system_validations)
    
    while IFS= read -r cmd; do
        if [[ -n "$cmd" ]]; then
            total=$((total + 1))
            if run_validation "$cmd" "System Validation"; then
                passed=$((passed + 1))
            else
                failed=$((failed + 1))
            fi
        fi
    done <<< "$system_commands"
    
    # 2. Run project validations if available
    if [[ -f "TASK.md" ]]; then
        info "📋 Project Task Validations"
        local project_commands
        project_commands=$(get_all_validation_commands)
        
        while IFS= read -r cmd; do
            if [[ -n "$cmd" ]]; then
                total=$((total + 1))
                if run_validation "$cmd" "Project Validation"; then
                    passed=$((passed + 1))
                else
                    failed=$((failed + 1))
                fi
            fi
        done <<< "$project_commands"
    fi
    
    # Show summary
    echo ""
    echo "=================================================="
    info "📊 COMPLETE VALIDATION SUMMARY"
    echo "=================================================="
    echo "Total Commands: $total"
    echo "Passed: $passed"
    echo "Failed: $failed"
    
    if [[ $failed -eq 0 ]]; then
        log "🎉 ALL validations PASSED!"
        return 0
    else
        error "❌ $failed validation(s) FAILED!"
        return 1
    fi
}

# Run quick validations (essential checks only)
run_quick_validations() {
    echo "⚡ Running quick validations..."
    
    local total=0
    local passed=0
    local failed=0
    
    # Essential validations
    local quick_commands=(
        "python3 .claude/scripts/validate_context_system.py"
        "python3 .claude/scripts/integration_manager.py --sync-all"
    )
    
    # Add make commands if Makefile exists
    if [[ -f "Makefile" ]]; then
        quick_commands+=("make lint" "make test")
    fi
    
    for cmd in "${quick_commands[@]}"; do
        total=$((total + 1))
        if run_validation "$cmd" "Quick Check $total"; then
            passed=$((passed + 1))
        else
            failed=$((failed + 1))
        fi
    done
    
    # Show summary
    echo ""
    echo "=================================================="
    info "📊 QUICK VALIDATION SUMMARY"
    echo "=================================================="
    echo "Total Commands: $total"
    echo "Passed: $passed"
    echo "Failed: $failed"
    
    if [[ $failed -eq 0 ]]; then
        log "⚡ Quick validations PASSED!"
        return 0
    else
        error "❌ $failed quick validation(s) FAILED!"
        return 1
    fi
}

# List available validations
list_validations() {
    echo ""
    info "📋 Available Validation Commands:"
    echo ""
    
    # System validations
    echo "🏗️  CCL System:"
    get_system_validations | while read -r cmd; do
        echo "  $cmd"
    done
    echo ""
    
    # Focused task validations
    local focused_commands
    focused_commands=$(get_focused_task_validations)
    if [[ -n "$focused_commands" ]]; then
        echo "🎯 Focused Task:"
        echo "$focused_commands" | while read -r cmd; do
            echo "  $cmd"
        done
        echo ""
    fi
    
    # All project validations
    if [[ -f "TASK.md" ]]; then
        echo "📋 All Project Tasks:"
        get_all_validation_commands | while read -r cmd; do
            echo "  $cmd"
        done
        echo ""
    fi
}

main() {
    local mode="${1:-focused}"
    
    case "$mode" in
        "focused"|"current"|"task")
            run_focused_validations
            ;;
        "all"|"complete"|"full")
            run_all_validations
            ;;
        "quick"|"fast"|"essential")
            run_quick_validations
            ;;
        "list"|"show")
            list_validations
            ;;
        *)
            error "Unknown validation mode: $mode"
            show_help
            exit 1
            ;;
    esac
}

# Show help
show_help() {
    cat << EOF
Usage: .claude/commands/validate-current-work [MODE] [OPTIONS]

Run validation commands for current work or project

MODES:
  focused        Run validations for currently focused task (default)
  all            Run all available validations
  quick          Run essential/quick validations only
  list           List all available validation commands

OPTIONS:
  -h, --help     Show this help message
  -v, --verbose  Verbose output

DESCRIPTION:
  This command runs validation commands based on the selected mode:
  
  - focused: Runs validation commands for the currently focused task
  - all: Runs CCL system validations + all project task validations
  - quick: Runs essential validations for fast feedback
  - list: Shows all available validation commands

EXAMPLES:
  .claude/commands/validate-current-work                # Run focused validations
  .claude/commands/validate-current-work all            # Run all validations
  .claude/commands/validate-current-work quick          # Run quick validations
  .claude/commands/validate-current-work list           # List validations

EXIT CODES:
  0    All validations passed
  1    One or more validations failed

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        -*)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            # This is the mode
            main "$1"
            exit $?
            ;;
    esac
done

# Default: run focused validations
main "focused"